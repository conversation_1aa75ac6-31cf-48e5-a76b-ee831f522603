<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal - <PERSON><PERSON> Responsa</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 2rem;
            background: #f5f5f5;
        }
        
        .test-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            cursor: pointer;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 1rem;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }
        
        .modal-body {
            padding: 1.5rem;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #1d4ed8;
        }
        
        .letter-details {
            background: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <h1>Modal Test Page</h1>
    <p>Click on the test cards below to test the modal functionality:</p>
    
    <div class="test-card" onclick="openTestModal('5744', '23455')">
        <h3>Test Letter 1</h3>
        <p>Year: 5744, Letter ID: 23455</p>
        <p>Click to open modal</p>
    </div>
    
    <div class="test-card" onclick="openTestModal('5716', '20745')">
        <h3>Test Letter 2</h3>
        <p>Year: 5716, Letter ID: 20745</p>
        <p>Click to open modal</p>
    </div>
    
    <div class="test-card" onclick="openTestModal('5703', '25292')">
        <h3>Test Letter 3</h3>
        <p>Year: 5703, Letter ID: 25292</p>
        <p>Click to open modal</p>
    </div>
    
    <!-- Modal -->
    <div id="testModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Letter Details</h3>
                <button class="modal-close" onclick="closeTestModal()">×</button>
            </div>
            <div class="modal-body">
                <div id="letterViewer">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function openTestModal(year, letterId) {
            console.log('Opening test modal for:', year, letterId);
            
            const modal = document.getElementById('testModal');
            const modalTitle = document.getElementById('modalTitle');
            const letterViewer = document.getElementById('letterViewer');
            
            modalTitle.textContent = `Letter ${year}/${letterId}`;
            
            // Create test content
            letterViewer.innerHTML = `
                <div class="letter-details">
                    <div class="detail-row">
                        <strong>Year:</strong> ${year}
                    </div>
                    <div class="detail-row">
                        <strong>Letter ID:</strong> ${letterId}
                    </div>
                    <div class="detail-row">
                        <strong>Status:</strong> Test Modal Working!
                    </div>
                </div>
                <div style="text-align: center; padding: 2rem;">
                    <h4>Modal is working correctly!</h4>
                    <p>This confirms the modal functionality is operational.</p>
                    <button class="btn" onclick="alert('Button clicked!')">Test Button</button>
                </div>
            `;
            
            // Show modal
            modal.style.display = 'flex';
            console.log('Modal should now be visible');
        }
        
        function closeTestModal() {
            console.log('Closing test modal');
            const modal = document.getElementById('testModal');
            modal.style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('testModal');
            if (e.target === modal) {
                closeTestModal();
            }
        });
        
        console.log('Test page loaded successfully');
    </script>
</body>
</html>
