{"secrets": {"found_secrets": [{"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\all_letters_download.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\all_letters_download.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\all_letters_download.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\all_letters_download.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\all_letters_download.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\all_letters_download.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\all_letters_download.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\app.js", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "App-specific Token", "value": "firebaseapp", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "default-rtdb", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "firebaseapp", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "firebaseapp", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "default-rtdb", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "default-rtdb", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "firebaseapp", "file": ".\\app.js", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\firebase_exploration_results.json", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\firebase_exploration_results.json", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\firebase_exploration_results.json", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\firebase_exploration_results.json", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "App-specific Token", "value": "cloudfunctions", "file": ".\\firebase_exploration_results.json", "pattern": "rebberesponsa[._-]([A-Za-z0-9_-]+)"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\firebase_extraction_results.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\firebase_extraction_results.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\working_firebase_access.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\working_firebase_access.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\working_firebase_access.json", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\website_archive\\config.js", "pattern": "AIza[0-9A-Za-z_-]{35}"}, {"type": "Google API Key", "value": "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "file": ".\\website_archive\\script.js", "pattern": "AIza[0-9A-Za-z_-]{35}"}]}, "firebase_configs": {}, "analysis_success": false}