#!/usr/bin/env python3
"""
Firebase Letter Access
Uses the letter IDs from the app URLs to access Firebase directly
"""

import requests
import json
import time
from datetime import datetime

def test_firebase_endpoints_with_letter_ids():
    """Test Firebase endpoints using the actual letter IDs from the app"""
    
    # Letter IDs from your URLs
    letter_data = [
        {'year': '5744', 'id': '23455'},
        {'year': '5711', 'id': '29797'},
        {'year': '5711', 'id': '20587'},
        {'year': '5711', 'id': '20586'},
        {'year': '5716', 'id': '20745'},
        {'year': '5743', 'id': '23338'},
    ]
    
    # Possible Firebase project names (more targeted based on the app)
    project_names = [
        'rebbe-responsa',
        'rebberesponsa',
        'responsa-app',
        'chabad-responsa',
        'igrot-kodesh',
        'igros-kodesh',
        'rebbe-letters'
    ]
    
    # Firebase endpoint patterns
    endpoint_patterns = [
        # Firestore patterns
        'https://firestore.googleapis.com/v1/projects/{project}/databases/(default)/documents/letters/{year}_{id}',
        'https://firestore.googleapis.com/v1/projects/{project}/databases/(default)/documents/letters/{id}',
        'https://firestore.googleapis.com/v1/projects/{project}/databases/(default)/documents/responsa/{year}_{id}',
        'https://firestore.googleapis.com/v1/projects/{project}/databases/(default)/documents/years/{year}/letters/{id}',
        
        # Firebase Storage patterns
        'https://firebasestorage.googleapis.com/v0/b/{project}.appspot.com/o/letters%2F{year}%2F{id}.json?alt=media',
        'https://firebasestorage.googleapis.com/v0/b/{project}.appspot.com/o/letters%2F{id}.json?alt=media',
        'https://firebasestorage.googleapis.com/v0/b/{project}.appspot.com/o/responsa%2F{year}_{id}.json?alt=media',
        'https://firebasestorage.googleapis.com/v0/b/{project}.appspot.com/o/data%2Fletters%2F{year}%2F{id}.json?alt=media',
        
        # Realtime Database patterns
        'https://{project}-default-rtdb.firebaseio.com/letters/{year}/{id}.json',
        'https://{project}-default-rtdb.firebaseio.com/letters/{id}.json',
        'https://{project}-default-rtdb.firebaseio.com/responsa/{year}_{id}.json',
        'https://{project}-default-rtdb.firebaseio.com/years/{year}/letters/{id}.json',
    ]
    
    print("🔍 Testing Firebase endpoints with actual letter IDs...")
    print("-" * 60)
    
    results = []
    
    for project in project_names:
        print(f"\n📍 Testing project: {project}")
        
        for letter in letter_data:
            year = letter['year']
            letter_id = letter['id']
            
            print(f"  📄 Letter {year}/{letter_id}:")
            
            for pattern in endpoint_patterns:
                url = pattern.format(project=project, year=year, id=letter_id)
                
                result = test_endpoint(url)
                results.append(result)
                
                if result['success']:
                    print(f"    ✅ FOUND: {result['endpoint_type']}")
                    print(f"       URL: {url}")
                    print(f"       Content: {result['content'][:100]}...")
                    
                    # If we found one, try a few more from the same pattern
                    if result['is_json']:
                        print(f"    🎯 JSON data found! This might be the pattern.")
                        return results  # Found working pattern, return immediately
                        
                elif result['status_code'] == 403:
                    print(f"    🔒 EXISTS (auth required): {result['endpoint_type']}")
                
                time.sleep(0.2)  # Small delay between requests
    
    return results

def test_endpoint(url):
    """Test a specific Firebase endpoint"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Origin': 'https://rebberesponsa.app',
        'Referer': 'https://rebberesponsa.app/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        # Determine endpoint type
        endpoint_type = "Unknown"
        if "firestore.googleapis.com" in url:
            endpoint_type = "Firestore"
        elif "firebasestorage.googleapis.com" in url:
            endpoint_type = "Firebase Storage"
        elif "firebaseio.com" in url:
            endpoint_type = "Realtime Database"
        
        result = {
            'url': url,
            'endpoint_type': endpoint_type,
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'content': response.text,
            'content_length': len(response.content),
            'is_json': False
        }
        
        # Try to parse as JSON
        if result['success']:
            try:
                json_data = json.loads(response.text)
                result['is_json'] = True
                result['json_data'] = json_data
            except:
                pass
        
        return result
        
    except Exception as e:
        return {
            'url': url,
            'endpoint_type': 'Unknown',
            'status_code': 'ERROR',
            'success': False,
            'content': '',
            'error': str(e),
            'is_json': False
        }

def test_public_firebase_endpoints():
    """Test if there are any public Firebase endpoints we can access"""
    
    print("\n🔍 Testing for public Firebase endpoints...")
    print("-" * 60)
    
    # Try some common public endpoints
    public_endpoints = [
        'https://firestore.googleapis.com/v1/projects/rebbe-responsa/databases/(default)/documents',
        'https://firestore.googleapis.com/v1/projects/rebberesponsa/databases/(default)/documents',
        'https://rebbe-responsa-default-rtdb.firebaseio.com/.json',
        'https://rebberesponsa-default-rtdb.firebaseio.com/.json',
        'https://firebasestorage.googleapis.com/v0/b/rebbe-responsa.appspot.com/o',
        'https://firebasestorage.googleapis.com/v0/b/rebberesponsa.appspot.com/o',
    ]
    
    public_results = []
    
    for url in public_endpoints:
        print(f"Testing: {url}")
        result = test_endpoint(url)
        public_results.append(result)
        
        if result['success']:
            print(f"  ✅ SUCCESS: {result['content_length']} bytes")
            if result['is_json']:
                print(f"  📄 JSON response found!")
        elif result['status_code'] == 403:
            print(f"  🔒 EXISTS but requires authentication")
        else:
            print(f"  ❌ {result['status_code']}")
        
        time.sleep(0.5)
    
    return public_results

def main():
    print("Firebase Letter Access Tool")
    print("=" * 60)
    print("Using letter IDs from the app URLs to find Firebase backend...")
    print()
    
    # Test with actual letter IDs
    letter_results = test_firebase_endpoints_with_letter_ids()
    
    # Test public endpoints
    public_results = test_public_firebase_endpoints()
    
    # Analyze results
    print("\n" + "=" * 60)
    print("RESULTS SUMMARY")
    print("=" * 60)
    
    successful_requests = [r for r in letter_results + public_results if r['success']]
    auth_required = [r for r in letter_results + public_results if r['status_code'] == 403]
    
    if successful_requests:
        print(f"\n✅ Found {len(successful_requests)} accessible endpoints:")
        for result in successful_requests:
            print(f"  📍 {result['endpoint_type']}: {result['url']}")
            if result['is_json']:
                print(f"     📄 JSON data available")
    
    if auth_required:
        print(f"\n🔒 Found {len(auth_required)} endpoints requiring authentication:")
        for result in auth_required:
            print(f"  🔑 {result['endpoint_type']}: {result['url']}")
    
    if not successful_requests and not auth_required:
        print("\n❌ No accessible Firebase endpoints found with the tested patterns.")
        print("\nThis could mean:")
        print("- The project uses a different naming convention")
        print("- All endpoints require authentication")
        print("- The data is stored in a different structure")
        print("- The project uses custom security rules")
    
    # Save results
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'letter_endpoint_tests': letter_results,
        'public_endpoint_tests': public_results,
        'summary': {
            'successful_requests': len(successful_requests),
            'auth_required_requests': len(auth_required),
            'total_tests': len(letter_results) + len(public_results)
        }
    }
    
    with open('firebase_letter_access_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n💾 Results saved to: firebase_letter_access_results.json")
    
    if successful_requests:
        print(f"\n🎯 NEXT STEPS:")
        print("We found accessible endpoints! We can now:")
        print("1. Extract the letter data structure")
        print("2. Identify all available letters")
        print("3. Download the complete letter database")

if __name__ == "__main__":
    main()
