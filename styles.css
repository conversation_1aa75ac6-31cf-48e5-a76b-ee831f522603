/* Modern Full-Screen Web App Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Color Palette */
    --primary: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary: #64748b;
    --accent: #f59e0b;

    /* Backgrounds */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #0f172a;

    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #94a3b8;
    --text-white: #ffffff;

    /* Borders */
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #cbd5e1;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Typography */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-serif: 'Crimson Text', Georgia, serif;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Transitions */
    --transition: all 0.2s ease-in-out;
    --transition-fast: all 0.15s ease-in-out;
    --transition-slow: all 0.3s ease-in-out;
}

html {
    height: 100%;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-secondary);
    height: 100%;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
}

/* Header Styles */
.main-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-4) var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-8);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    flex-shrink: 0;
}

.logo {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.title-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.title-section p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    flex: 1;
    justify-content: flex-end;
}

.search-box {
    position: relative;
    max-width: 400px;
    width: 100%;
}

.search-box .search-icon {
    position: absolute;
    left: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

.search-box input {
    width: 100%;
    padding: var(--space-3) var(--space-10) var(--space-3) var(--space-10);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    font-size: 0.875rem;
    color: var(--text-primary);
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    background: var(--bg-primary);
}

.search-box .clear-btn {
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.search-box .clear-btn:hover {
    color: var(--text-secondary);
    background: var(--bg-tertiary);
}

.filter-controls {
    display: flex;
    gap: var(--space-3);
}

.filter-select {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--bg-primary);
    font-size: 0.875rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.filter-select:hover {
    border-color: var(--border-dark);
}

/* Navigation Styles */
.main-nav {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border);
    position: sticky;
    top: 89px;
    z-index: 90;
}

.nav-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    gap: var(--space-2);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-6);
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition);
    position: relative;
}

.nav-btn:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

.nav-btn.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
    background: var(--bg-secondary);
}

.nav-btn .count {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    min-width: 20px;
    text-align: center;
}

.nav-btn.active .count {
    background: var(--primary);
    color: var(--text-white);
}

/* Main Content Styles */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6);
    width: 100%;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border);
}

.section-header h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-stats {
    display: flex;
    gap: var(--space-6);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: var(--space-1);
}

/* Letters Grid */
.letters-container {
    width: 100%;
}

.letters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.letter-card {
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.letter-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.letter-card:active {
    transform: translateY(0);
}

.letter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.letter-year {
    background: var(--primary);
    color: var(--text-white);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.05em;
}

.letter-id {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    font-family: var(--font-mono);
}

.letter-preview {
    color: var(--text-primary);
    font-size: 0.875rem;
    line-height: 1.6;
    margin-bottom: var(--space-4);
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-family: var(--font-serif);
}

.letter-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--text-muted);
    padding-top: var(--space-3);
    border-top: 1px solid var(--border-light);
}

.letter-category {
    background: var(--bg-tertiary);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.letter-size {
    font-weight: 500;
    font-family: var(--font-mono);
}

/* Loading States */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-16);
    text-align: center;
    grid-column: 1 / -1;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-4);
}

.loading-placeholder p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Load More Button */
.load-more-container {
    display: flex;
    justify-content: center;
    margin-top: var(--space-8);
}

.load-more-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.load-more-btn:hover {
    border-color: var(--primary);
    color: var(--primary);
    box-shadow: var(--shadow);
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-16);
    text-align: center;
    grid-column: 1 / -1;
}

.empty-state svg {
    width: 64px;
    height: 64px;
    color: var(--text-muted);
    margin-bottom: var(--space-6);
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.empty-state p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    max-width: 400px;
}

/* Topics Grid */
.topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--space-4);
}

.topic-card {
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.topic-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.topic-icon {
    width: 48px;
    height: 48px;
    background: var(--primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
    color: var(--text-white);
    font-size: 1.5rem;
}

.topic-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.topic-count {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
}

.modal-overlay.active {
    display: flex;
    animation: fadeIn 0.3s ease;
}

.modal-container {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-6);
    border-bottom: 1px solid var(--border);
    background: var(--bg-secondary);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius);
    transition: var(--transition);
}

.close-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-actions {
    display: flex;
    gap: var(--space-2);
}

.action-btn {
    background: none;
    border: 1px solid var(--border);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius);
    transition: var(--transition);
}

.action-btn:hover {
    border-color: var(--primary);
    color: var(--primary);
    background: var(--bg-secondary);
}

.modal-body {
    padding: var(--space-6);
    max-height: calc(90vh - 120px);
    overflow-y: auto;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.content-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.item-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-4);
        align-items: stretch;
    }

    .header-actions {
        justify-content: stretch;
        flex-direction: column;
        gap: var(--space-4);
    }

    .filter-controls {
        justify-content: stretch;
    }

    .filter-select {
        flex: 1;
    }

    .main-nav {
        top: 140px;
    }

    .nav-content {
        padding: 0 var(--space-4);
    }

    .nav-btn {
        padding: var(--space-3) var(--space-4);
        font-size: 0.75rem;
    }

    .main-content {
        padding: var(--space-4);
    }

    .letters-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-4);
    }

    .section-stats {
        justify-content: center;
    }

    .modal-container {
        margin: var(--space-4);
        max-height: calc(100vh - 2rem);
    }
}

@media (max-width: 480px) {
    .logo-section {
        gap: var(--space-3);
    }

    .logo {
        width: 40px;
        height: 40px;
    }

    .title-section h1 {
        font-size: 1.25rem;
    }

    .search-box input {
        font-size: 0.75rem;
        padding: var(--space-2) var(--space-8) var(--space-2) var(--space-8);
    }

    .nav-btn {
        flex-direction: column;
        gap: var(--space-1);
        padding: var(--space-2);
    }

    .nav-btn svg {
        width: 16px;
        height: 16px;
    }

    .letter-card {
        padding: var(--space-4);
    }

    .section-header h2 {
        font-size: 1.5rem;
    }
}

/* Letter Details in Modal */
.letter-details {
    margin-bottom: var(--space-6);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3) 0;
    border-bottom: 1px solid var(--border-light);
}

.detail-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.detail-value {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-family: var(--font-mono);
}

.letter-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

.preview-text {
    background: var(--bg-secondary);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    font-family: var(--font-serif);
    line-height: 1.7;
    color: var(--text-primary);
    margin-bottom: var(--space-6);
    white-space: pre-line;
}

.pdf-placeholder {
    text-align: center;
    padding: var(--space-8);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg);
    border: 2px dashed var(--border);
}

.pdf-placeholder svg {
    color: var(--text-muted);
    margin-bottom: var(--space-4);
}

.pdf-placeholder p {
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
    font-size: 0.875rem;
}

.pdf-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: var(--primary);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.pdf-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.pdf-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

/* Insights Styles */
.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-6);
}

.insight-card {
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    transition: var(--transition);
}

.insight-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.insight-header {
    margin-bottom: var(--space-4);
}

.insight-date {
    background: var(--accent);
    color: var(--text-white);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
}

.insight-text {
    font-family: var(--font-serif);
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-primary);
    margin-bottom: var(--space-4);
    font-style: italic;
}

.insight-actions {
    display: flex;
    justify-content: flex-end;
}

.save-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: none;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.save-btn:hover {
    border-color: var(--primary);
    color: var(--primary);
    background: var(--bg-secondary);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.loading-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideDown {
    from { 
        opacity: 0;
        transform: translateY(-10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(100%);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* Letters Grid */
.letters-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.letter-card {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.letter-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.letter-card:active {
    transform: translateY(0);
}

.letter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.letter-year {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.letter-id {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.letter-preview {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.letter-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.letter-category {
    background: var(--border-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.letter-size {
    font-weight: 500;
}

/* Insights List */
.insights-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.insight-card {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    position: relative;
}

.insight-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.insight-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.insight-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.insight-text {
    color: var(--text-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    font-style: italic;
    position: relative;
    padding-left: var(--spacing-md);
}

.insight-text::before {
    content: '"';
    position: absolute;
    left: 0;
    top: 0;
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    font-weight: bold;
    line-height: 1;
}

/* Topics Grid */
.topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-md);
}

.topic-card {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.topic-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.topic-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-color);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: white;
}

.topic-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.topic-count {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.empty-state svg {
    width: 64px;
    height: 64px;
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    max-width: 280px;
}

/* Load More Button */
.load-more-btn {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--surface);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-top: var(--spacing-lg);
}

.load-more-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 480px) {
    .header-content,
    .search-container,
    .tab-navigation,
    .main-content {
        max-width: 100%;
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .topics-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
}

.modal-overlay.active {
    display: flex;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--surface);
    border-radius: var(--radius-xl);
    width: 100%;
    max-width: 480px;
    max-height: 90vh;
    margin: var(--spacing-lg);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--surface);
    position: sticky;
    top: 0;
    z-index: 1;
}

.back-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    background: var(--border-light);
    color: var(--primary-color);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
    text-align: center;
    margin: 0 var(--spacing-md);
}

.modal-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.letter-details {
    margin-bottom: var(--spacing-xl);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-light);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.detail-value {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.letter-content {
    background: var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.letter-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.preview-text {
    color: var(--text-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    white-space: pre-line;
    margin-bottom: var(--spacing-lg);
}

.pdf-placeholder {
    text-align: center;
    padding: var(--spacing-xl);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--surface);
}

.pdf-placeholder svg {
    width: 48px;
    height: 48px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.pdf-placeholder p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-lg);
}

.pdf-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.pdf-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.pdf-btn:active {
    transform: translateY(0);
}

@media (max-width: 480px) {
    .header-content,
    .search-container,
    .tab-navigation,
    .main-content {
        max-width: 100%;
    }

    .main-content {
        padding: var(--spacing-md);
    }

    .topics-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .modal-content {
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
    }

    .modal-header {
        border-radius: 0;
    }
}

@media (min-width: 481px) {
    body {
        background: #f0f0f0;
    }

    .app-header,
    .tab-navigation {
        box-shadow: var(--shadow-medium);
    }

    .main-content {
        background: var(--surface);
        border-radius: var(--radius-xl);
        margin-top: 160px;
        box-shadow: var(--shadow-light);
    }
}
