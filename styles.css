/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* App Colors - Based on mobile app design */
    --primary-color: #1B336A;
    --primary-light: #2A4A7A;
    --primary-dark: #0F1F3A;
    --accent-color: #4A90E2;
    --background: #F8F9FA;
    --surface: #FFFFFF;
    --text-primary: #1A1A1A;
    --text-secondary: #666666;
    --text-muted: #999999;
    --border-color: #E5E5E5;
    --border-light: #F0F0F0;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.16);
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 24px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.25s ease;
    --transition-slow: 0.35s ease;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--background);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* App Header */
.app-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--surface);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    box-shadow: var(--shadow-light);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    max-width: 480px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.app-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.app-icon svg {
    width: 100%;
    height: 100%;
}

.app-title h1 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.app-title .subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 400;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.icon-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.icon-btn:hover {
    background: var(--border-light);
    color: var(--primary-color);
}

.icon-btn:active {
    transform: scale(0.95);
}

/* Search Container */
.search-container {
    padding: 0 var(--spacing-md) var(--spacing-md);
    max-width: 480px;
    margin: 0 auto;
    display: none;
}

.search-container.active {
    display: block;
    animation: slideDown 0.3s ease;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
}

.search-icon {
    color: var(--text-muted);
    margin-right: var(--spacing-sm);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: var(--font-size-base);
    color: var(--text-primary);
    outline: none;
}

.search-input::placeholder {
    color: var(--text-muted);
}

.clear-search {
    display: none;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.clear-search:hover {
    background: var(--border-color);
}

/* Tab Navigation */
.tab-navigation {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: var(--surface);
    border-bottom: 1px solid var(--border-color);
    z-index: 999;
    display: flex;
    max-width: 480px;
    margin: 0 auto;
}

.tab-btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-sm);
    border: none;
    background: transparent;
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.tab-btn svg {
    width: 24px;
    height: 24px;
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
}

.tab-btn:hover:not(.active) {
    color: var(--text-secondary);
}

/* Main Content */
.main-content {
    margin-top: 140px;
    padding: var(--spacing-lg) var(--spacing-md);
    max-width: 480px;
    margin-left: auto;
    margin-right: auto;
    min-height: calc(100vh - 140px);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
}

.content-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.item-count {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

/* Letters Grid */
.letters-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.letter-card {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.letter-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.letter-card:active {
    transform: translateY(0);
}

.letter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.letter-year {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    letter-spacing: 0.5px;
}

.letter-id {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.letter-preview {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.letter-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-light);
}

.letter-category {
    background: var(--border-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.letter-size {
    font-weight: 500;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* Loading States */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.loading-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Load More Button */
.load-more-btn {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-top: var(--spacing-lg);
}

.load-more-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    box-shadow: var(--shadow-light);
}

/* Filter Panel */
.filter-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: none;
    align-items: flex-end;
}

.filter-panel.active {
    display: flex;
    animation: fadeIn 0.3s ease;
}

.filter-content {
    background: var(--surface);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: var(--spacing-lg);
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    max-height: 70vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.filter-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.close-filter {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.filter-section {
    margin-bottom: var(--spacing-xl);
}

.filter-section h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.year-filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.filter-chip {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--surface);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-chip:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.filter-chip.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.sort-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.radio-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    font-size: var(--font-size-base);
    color: var(--text-primary);
}

.radio-option input[type="radio"] {
    width: 20px;
    height: 20px;
    accent-color: var(--primary-color);
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
}

.modal-overlay.active {
    display: flex;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: var(--surface);
    border-radius: var(--radius-xl);
    max-width: 480px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.back-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.modal-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: calc(90vh - 120px);
    overflow-y: auto;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.empty-state svg {
    width: 64px;
    height: 64px;
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Topics Grid */
.topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-md);
}

.topic-card {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.topic-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.topic-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
}

.topic-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.topic-count {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}
