#!/usr/bin/env python3
"""
Extract Firebase Configuration from APK
Searches for Firebase project configuration in the APK files
"""

import re
import os
import json

def search_file_for_patterns(file_path, patterns):
    """Search a file for specific patterns"""
    results = []
    
    try:
        # Try reading as text first
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                results.append({
                    'file': file_path,
                    'pattern': pattern_name,
                    'matches': list(set(matches))  # Remove duplicates
                })
    except:
        # If text reading fails, try binary reading for specific patterns
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                
            # Convert to string for pattern matching
            content_str = content.decode('utf-8', errors='ignore')
            
            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, content_str, re.IGNORECASE)
                if matches:
                    results.append({
                        'file': file_path,
                        'pattern': pattern_name,
                        'matches': list(set(matches))
                    })
        except:
            pass
    
    return results

def extract_firebase_config():
    """Extract Firebase configuration from APK files"""
    
    # More specific patterns to search for
    patterns = {
        'project_id_json': r'"project_id"\s*:\s*"([a-zA-Z0-9][a-zA-Z0-9-]{4,30})"',
        'storage_bucket_json': r'"storageBucket"\s*:\s*"([a-zA-Z0-9][a-zA-Z0-9-]{4,30}\.appspot\.com)"',
        'database_url_json': r'"databaseURL"\s*:\s*"https://([a-zA-Z0-9][a-zA-Z0-9-]{4,30})-default-rtdb\.firebaseio\.com"',
        'api_key_json': r'"apiKey"\s*:\s*"(AIza[a-zA-Z0-9_-]{35})"',
        'app_id_json': r'"appId"\s*:\s*"(1:[0-9]+:android:[a-zA-Z0-9]+)"',
        'messaging_sender_id_json': r'"messagingSenderId"\s*:\s*"([0-9]+)"',
        'firebase_url_direct': r'https://([a-zA-Z0-9][a-zA-Z0-9-]{4,30})-default-rtdb\.firebaseio\.com',
        'storage_url_direct': r'firebasestorage\.googleapis\.com/v0/b/([a-zA-Z0-9][a-zA-Z0-9-]{4,30})\.appspot\.com',
        'functions_url_direct': r'([a-zA-Z0-9][a-zA-Z0-9-]{4,30})\.cloudfunctions\.net',
        'project_number_json': r'"projectNumber"\s*:\s*"([0-9]+)"',
        'google_app_id': r'google_app_id["\s]*[=:]["\s]*([0-9]+:[0-9]+:android:[a-zA-Z0-9]+)',
        'firebase_project_string': r'firebase[_-]?project[_-]?id["\s]*[=:]["\s]*([a-zA-Z0-9][a-zA-Z0-9-]{4,30})'
    }
    
    # Files to search
    files_to_search = [
        'classes.dex',
        'resources.arsc',
        'res/raw/firebase_common_keep.xml'
    ]
    
    # Add any other files that might exist
    for root, dirs, files in os.walk('.'):
        for file in files:
            if any(keyword in file.lower() for keyword in ['firebase', 'google', 'config', 'services']):
                file_path = os.path.join(root, file)
                if file_path not in files_to_search:
                    files_to_search.append(file_path)
    
    all_results = []
    
    print("Searching for Firebase configuration...")
    print("-" * 50)
    
    for file_path in files_to_search:
        if os.path.exists(file_path):
            print(f"Searching: {file_path}")
            results = search_file_for_patterns(file_path, patterns)
            all_results.extend(results)
        else:
            print(f"File not found: {file_path}")
    
    return all_results

def analyze_results(results):
    """Analyze the extracted results to find the most likely project configuration"""

    config = {}

    # Filter out common false positives
    false_positives = {'for', 'match', 'urls', 'bucket', 'None', 'config', 'project', 'the', 'and', 'or', 'if', 'else', 'true', 'false', 'null'}

    for result in results:
        pattern = result['pattern']
        matches = result['matches']

        if matches:
            # Filter out false positives
            valid_matches = [match for match in matches if match.lower() not in false_positives and len(match) > 3]

            if valid_matches:
                print(f"\n✅ Found {pattern}:")
                for match in valid_matches:
                    print(f"   {match}")

                    # Store the first valid match for each pattern type
                    if pattern not in config:
                        config[pattern] = match

    return config

def construct_firebase_urls(config):
    """Construct Firebase URLs from the extracted configuration"""
    
    urls = {}
    
    # Try to find project ID from various sources
    project_id = None
    for key in ['project_id', 'project_id_alt']:
        if key in config:
            project_id = config[key]
            break
    
    # Extract project ID from URLs if not found directly
    if not project_id:
        for key, value in config.items():
            if 'firebase_url' in key:
                match = re.search(r'([a-zA-Z0-9-]+)\.firebaseio\.com', value)
                if match:
                    project_id = match.group(1)
                    break
            elif 'storage_url' in key:
                match = re.search(r'/b/([a-zA-Z0-9.-]+)', value)
                if match:
                    bucket = match.group(1)
                    if bucket.endswith('.appspot.com'):
                        project_id = bucket.replace('.appspot.com', '')
                    break
    
    if project_id:
        urls['project_id'] = project_id
        urls['storage'] = f"https://firebasestorage.googleapis.com/v0/b/{project_id}.appspot.com/o"
        urls['firestore'] = f"https://firestore.googleapis.com/v1/projects/{project_id}/databases/(default)/documents"
        urls['realtime_db'] = f"https://{project_id}-default-rtdb.firebaseio.com/.json"
        urls['functions'] = f"https://us-central1-{project_id}.cloudfunctions.net"
    
    return urls

def main():
    print("Firebase Configuration Extractor")
    print("=" * 50)
    print("Analyzing APK files for Firebase configuration...")
    print()
    
    # Extract configuration
    results = extract_firebase_config()
    
    if results:
        print("\n" + "=" * 50)
        print("FOUND FIREBASE CONFIGURATION")
        print("=" * 50)
        
        # Analyze results
        config = analyze_results(results)
        
        # Construct URLs
        urls = construct_firebase_urls(config)
        
        if urls:
            print(f"\n🎯 FIREBASE PROJECT ENDPOINTS:")
            print("-" * 30)
            for service, url in urls.items():
                print(f"{service}: {url}")
            
            # Save results
            output = {
                'extracted_config': config,
                'firebase_urls': urls,
                'raw_results': results
            }
            
            with open('firebase_config_extracted.json', 'w') as f:
                json.dump(output, f, indent=2)
            
            print(f"\n💾 Results saved to: firebase_config_extracted.json")
            
            # Test the URLs
            print(f"\n🔍 You can now test these URLs:")
            if 'project_id' in urls:
                print(f"1. Try accessing: {urls['storage']}")
                print(f"2. Try accessing: {urls['firestore']}")
                print(f"3. Try accessing: {urls['realtime_db']}")
        
        else:
            print("\n❌ Could not determine project ID from extracted data")
            print("Raw configuration found:")
            for key, value in config.items():
                print(f"  {key}: {value}")
    
    else:
        print("\n❌ No Firebase configuration found in APK files")
        print("The configuration might be:")
        print("- Obfuscated or encrypted")
        print("- Loaded dynamically at runtime")
        print("- Stored in a different format")

if __name__ == "__main__":
    main()
