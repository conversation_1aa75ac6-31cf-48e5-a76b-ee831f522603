#!/usr/bin/env python3
"""
Firebase Traffic Analyzer
Analyzes Fiddler capture files to extract Firebase API calls for letter data
"""

import json
import re
import sys
from urllib.parse import urlparse, parse_qs

def analyze_fiddler_log(log_file_path):
    """
    Analyze Fiddler log file to find Firebase-related requests
    """
    firebase_calls = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Look for Firebase-related URLs
        firebase_patterns = [
            r'https://firebasestorage\.googleapis\.com[^\s]*',
            r'https://[^-]+-[^-]+\.cloudfunctions\.net[^\s]*',
            r'https://[^-]+-default-rtdb\.firebaseio\.com[^\s]*',
            r'https://firestore\.googleapis\.com[^\s]*'
        ]
        
        for pattern in firebase_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                firebase_calls.append({
                    'url': match,
                    'type': identify_call_type(match)
                })
    
    except Exception as e:
        print(f"Error reading file: {e}")
        return []
    
    return firebase_calls

def identify_call_type(url):
    """
    Identify the type of Firebase call based on URL
    """
    if 'firebasestorage.googleapis.com' in url:
        return 'Firebase Storage'
    elif 'cloudfunctions.net' in url:
        return 'Cloud Functions'
    elif 'firebaseio.com' in url:
        return 'Realtime Database'
    elif 'firestore.googleapis.com' in url:
        return 'Firestore'
    else:
        return 'Unknown Firebase Service'

def extract_letter_endpoints(firebase_calls):
    """
    Extract potential letter-related endpoints
    """
    letter_endpoints = []
    
    for call in firebase_calls:
        url = call['url']
        
        # Look for letter-related keywords in URLs
        letter_keywords = ['letter', 'responsa', 'rebbe', 'insight', 'quote', 'text', 'content']
        
        for keyword in letter_keywords:
            if keyword.lower() in url.lower():
                letter_endpoints.append(call)
                break
    
    return letter_endpoints

def main():
    print("Firebase Traffic Analyzer")
    print("=" * 50)
    
    if len(sys.argv) != 2:
        print("Usage: python firebase_traffic_analyzer.py <fiddler_log_file>")
        print("\nTo export Fiddler log:")
        print("1. In Fiddler, select all sessions (Ctrl+A)")
        print("2. Go to File → Export Sessions → All Sessions")
        print("3. Choose 'HTTPArchive v1.2' format")
        print("4. Save as .har file")
        return
    
    log_file = sys.argv[1]
    
    print(f"Analyzing: {log_file}")
    print("-" * 30)
    
    firebase_calls = analyze_fiddler_log(log_file)
    
    if not firebase_calls:
        print("No Firebase calls found in the log file.")
        print("\nTips:")
        print("- Make sure you captured HTTPS traffic")
        print("- Ensure the app made network requests during capture")
        print("- Check if the log file format is correct")
        return
    
    print(f"Found {len(firebase_calls)} Firebase-related calls:")
    print()
    
    for i, call in enumerate(firebase_calls, 1):
        print(f"{i}. {call['type']}")
        print(f"   URL: {call['url']}")
        print()
    
    # Extract potential letter endpoints
    letter_endpoints = extract_letter_endpoints(firebase_calls)
    
    if letter_endpoints:
        print("=" * 50)
        print("POTENTIAL LETTER ENDPOINTS:")
        print("=" * 50)
        
        for i, endpoint in enumerate(letter_endpoints, 1):
            print(f"{i}. {endpoint['type']}")
            print(f"   URL: {endpoint['url']}")
            print()
    
    # Save results to JSON
    output_file = "firebase_endpoints.json"
    with open(output_file, 'w') as f:
        json.dump({
            'all_firebase_calls': firebase_calls,
            'letter_endpoints': letter_endpoints
        }, f, indent=2)
    
    print(f"Results saved to: {output_file}")

if __name__ == "__main__":
    main()
