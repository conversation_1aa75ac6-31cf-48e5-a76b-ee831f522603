<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Letters Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .letter-card {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .letter-card:hover {
            background: #f9f9f9;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .letter-header {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .letter-year {
            color: #2563eb;
        }
        .letter-id {
            color: #666;
        }
        .letter-preview {
            margin: 10px 0;
            color: #333;
            white-space: pre-line;
        }
        .letter-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #666;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .loading {
            background: #e3f2fd;
            color: #1976d2;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Rebbe Letters Loading Test</h1>
        
        <div id="status" class="status loading">
            Loading letters...
        </div>
        
        <div>
            <button onclick="loadFromJSON()">Load from JSON</button>
            <button onclick="loadSampleData()">Load Sample Data</button>
            <button onclick="clearLetters()">Clear</button>
        </div>
        
        <div id="letterCount">
            <strong>Letters loaded: 0</strong>
        </div>
        
        <div id="lettersContainer">
            <!-- Letters will be displayed here -->
        </div>
    </div>

    <script>
        let allLetters = [];
        
        async function loadFromJSON() {
            try {
                updateStatus('Loading from JSON file...', 'loading');
                const response = await fetch('./all_letters_download.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                
                if (data.all_items && Array.isArray(data.all_items)) {
                    allLetters = processLetterFiles(data.all_items);
                    updateStatus(`Successfully loaded ${allLetters.length} letters from JSON`, 'success');
                    displayLetters();
                } else {
                    throw new Error('Invalid JSON structure');
                }
            } catch (error) {
                console.error('Error loading JSON:', error);
                updateStatus(`Error loading JSON: ${error.message}`, 'error');
            }
        }
        
        function loadSampleData() {
            allLetters = [
                {
                    year: '5752',
                    letterId: '31435',
                    uuid: 'test-1',
                    fileName: '5752/31435/test-1',
                    size: 1234567,
                    downloadUrl: 'https://example.com/test1.pdf',
                    preview: 'Thank you for your letter. Concerning...',
                    category: 'Later Years'
                },
                {
                    year: '5752',
                    letterId: '23681',
                    uuid: 'test-2',
                    fileName: '5752/23681/test-2',
                    size: 987654,
                    downloadUrl: 'https://example.com/test2.pdf',
                    preview: 'Regarding your inquiry about...',
                    category: 'Later Years'
                }
            ];
            updateStatus(`Loaded ${allLetters.length} sample letters`, 'success');
            displayLetters();
        }
        
        function clearLetters() {
            allLetters = [];
            updateStatus('Letters cleared', 'loading');
            displayLetters();
        }
        
        function processLetterFiles(items) {
            const letters = [];
            
            items.forEach(item => {
                const name = item.name || item.fileName;
                if (!name) return;
                
                const decodedName = decodeURIComponent(name);
                const parts = decodedName.split('/');
                
                if (parts.length === 3) {
                    const [year, letterId, uuid] = parts;
                    
                    if (year.match(/^57\d{2}$/) && letterId.match(/^\d+$/)) {
                        const minSize = 500 * 1024;
                        const maxSize = 3 * 1024 * 1024;
                        const randomSize = Math.floor(Math.random() * (maxSize - minSize) + minSize);
                        
                        letters.push({
                            year: year,
                            letterId: letterId,
                            uuid: uuid,
                            fileName: decodedName,
                            size: parseInt(item.size) || randomSize,
                            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/english-letters.appspot.com/o/${encodeURIComponent(decodedName)}?alt=media`,
                            preview: generateLetterPreview(year, letterId),
                            category: categorizeByYear(year)
                        });
                    }
                }
            });
            
            letters.sort((a, b) => {
                if (a.year !== b.year) {
                    return b.year.localeCompare(a.year);
                }
                return parseInt(b.letterId) - parseInt(a.letterId);
            });
            
            return letters;
        }
        
        function generateLetterPreview(year, letterId) {
            const previews = [
                "ב\"ה\n\nIn response to your letter regarding...",
                "בעזהי\"ת\n\nYour question about the matter of...",
                "Dear Friend,\n\nI received your letter and...",
                "Regarding your inquiry about...",
                "In answer to your question...",
                "Thank you for your letter. Concerning..."
            ];
            
            const index = (parseInt(year) + parseInt(letterId)) % previews.length;
            return previews[index];
        }
        
        function categorizeByYear(year) {
            const yearNum = parseInt(year);
            if (yearNum >= 5740) return 'Later Years';
            if (yearNum >= 5720) return 'Middle Years';
            return 'Early Years';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function displayLetters() {
            const container = document.getElementById('lettersContainer');
            const countEl = document.getElementById('letterCount');
            
            countEl.innerHTML = `<strong>Letters loaded: ${allLetters.length}</strong>`;
            
            if (allLetters.length === 0) {
                container.innerHTML = '<p>No letters to display</p>';
                return;
            }
            
            container.innerHTML = '';
            
            // Show first 10 letters
            const lettersToShow = allLetters.slice(0, 10);
            
            lettersToShow.forEach(letter => {
                const card = document.createElement('div');
                card.className = 'letter-card';
                card.onclick = () => alert(`Clicked letter ${letter.year}/${letter.letterId}`);
                
                card.innerHTML = `
                    <div class="letter-header">
                        <span class="letter-year">${letter.year}</span>
                        <span class="letter-id">#${letter.letterId}</span>
                    </div>
                    <div class="letter-preview">${letter.preview}</div>
                    <div class="letter-meta">
                        <span class="letter-category">${letter.category}</span>
                        <span class="letter-size">${formatFileSize(letter.size)}</span>
                    </div>
                `;
                
                container.appendChild(card);
            });
            
            if (allLetters.length > 10) {
                const moreInfo = document.createElement('p');
                moreInfo.textContent = `... and ${allLetters.length - 10} more letters`;
                moreInfo.style.textAlign = 'center';
                moreInfo.style.color = '#666';
                container.appendChild(moreInfo);
            }
        }
        
        // Auto-load on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadFromJSON();
        });
    </script>
</body>
</html>
