#!/usr/bin/env python3
"""
Systematic Firebase Project Search
Uses various techniques to find the actual Firebase project
"""

import requests
import json
import time
import re
from itertools import product

def test_endpoint_with_details(url, description):
    """Test an endpoint and return detailed response information"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        
        result = {
            'url': url,
            'description': description,
            'status_code': response.status_code,
            'success': False,
            'response_preview': '',
            'content_type': response.headers.get('content-type', ''),
            'content_length': len(response.content)
        }
        
        if response.status_code == 200:
            result['success'] = True
            try:
                data = response.json()
                result['response_preview'] = str(data)[:300] + "..." if len(str(data)) > 300 else str(data)
                result['is_json'] = True
            except:
                result['response_preview'] = response.text[:300] + "..." if len(response.text) > 300 else response.text
                result['is_json'] = False
        elif response.status_code == 403:
            result['response_preview'] = "Forbidden - Project exists but requires authentication"
        elif response.status_code == 404:
            result['response_preview'] = "Not found"
        else:
            result['response_preview'] = f"Status {response.status_code}: {response.text[:100]}"
            
        return result
        
    except requests.exceptions.Timeout:
        return {'url': url, 'description': description, 'status_code': 'TIMEOUT', 'success': False, 'response_preview': 'Request timed out'}
    except Exception as e:
        return {'url': url, 'description': description, 'status_code': 'ERROR', 'success': False, 'response_preview': str(e)}

def generate_project_name_variations():
    """Generate possible project name variations for Rebbe Responsa app"""
    
    base_words = ['rebbe', 'responsa', 'chabad', 'lubavitch', 'letters', 'torah', 'jewish', 'igrot', 'kodesh']
    separators = ['', '-', '_']
    
    # Single words
    variations = base_words.copy()
    
    # Two word combinations
    for word1, word2 in product(base_words[:6], base_words[:6]):  # Limit to avoid too many combinations
        if word1 != word2:
            for sep in separators:
                variations.append(f"{word1}{sep}{word2}")
    
    # Add some specific likely names
    specific_names = [
        'igrot-kodesh',
        'igros-kodesh', 
        'rebberesponsa',
        'chabad-org',
        'chabad-app',
        'responsa-app',
        'rebbe-letters',
        'lubavitch-app'
    ]
    
    variations.extend(specific_names)
    
    # Remove duplicates and return first 50 to avoid too many requests
    return list(set(variations))[:50]

def test_firebase_project(project_id):
    """Test if a Firebase project exists and what services are available"""
    
    endpoints = [
        (f"https://firebasestorage.googleapis.com/v0/b/{project_id}.appspot.com/o", "Firebase Storage"),
        (f"https://{project_id}-default-rtdb.firebaseio.com/.json", "Realtime Database"),
        (f"https://firestore.googleapis.com/v1/projects/{project_id}/databases/(default)/documents", "Firestore"),
        (f"https://us-central1-{project_id}.cloudfunctions.net", "Cloud Functions (us-central1)"),
        (f"https://europe-west1-{project_id}.cloudfunctions.net", "Cloud Functions (europe-west1)"),
    ]
    
    results = []
    project_exists = False
    
    print(f"\n🔍 Testing project: {project_id}")
    
    for url, description in endpoints:
        result = test_endpoint_with_details(url, description)
        results.append(result)
        
        # Print immediate feedback
        if result['success']:
            print(f"  ✅ {description}: ACCESSIBLE")
            project_exists = True
        elif result['status_code'] == 403:
            print(f"  🔒 {description}: EXISTS (requires auth)")
            project_exists = True
        elif result['status_code'] == 412:
            print(f"  🔑 {description}: EXISTS (permission issue)")
            project_exists = True
        else:
            print(f"  ❌ {description}: {result['status_code']}")
        
        time.sleep(0.5)  # Be respectful to servers
    
    return project_exists, results

def search_for_letters_in_accessible_endpoints(results):
    """Search for letter-related data in accessible endpoints"""
    
    letter_data = []
    
    for result in results:
        if result['success'] and result.get('is_json'):
            try:
                # Look for letter-related content in the response
                response_text = result['response_preview'].lower()
                
                if any(keyword in response_text for keyword in ['letter', 'responsa', 'insight', 'quote', 'rebbe', 'year', 'month', 'day']):
                    letter_data.append({
                        'endpoint': result['url'],
                        'description': result['description'],
                        'preview': result['response_preview']
                    })
            except:
                pass
    
    return letter_data

def main():
    print("Systematic Firebase Project Search")
    print("=" * 60)
    print("Searching for the actual Firebase project used by Rebbe Responsa app...")
    print()
    
    # Generate project name variations
    project_variations = generate_project_name_variations()
    
    print(f"Testing {len(project_variations)} potential project names...")
    print("This may take a few minutes...")
    
    all_results = []
    found_projects = []
    
    for i, project_id in enumerate(project_variations, 1):
        print(f"\n[{i}/{len(project_variations)}] Testing: {project_id}")
        
        project_exists, results = test_firebase_project(project_id)
        
        if project_exists:
            found_projects.append({
                'project_id': project_id,
                'results': results
            })
            
            # Look for letter data in accessible endpoints
            letter_data = search_for_letters_in_accessible_endpoints(results)
            if letter_data:
                print(f"  🎯 FOUND LETTER DATA in {project_id}!")
                for data in letter_data:
                    print(f"     {data['description']}: {data['preview'][:100]}...")
        
        all_results.extend(results)
        
        # Small delay between projects
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("SEARCH COMPLETE")
    print("=" * 60)
    
    if found_projects:
        print(f"\n✅ Found {len(found_projects)} existing Firebase projects:")
        
        for project in found_projects:
            print(f"\n📍 Project: {project['project_id']}")
            
            accessible_services = [r for r in project['results'] if r['success']]
            auth_required_services = [r for r in project['results'] if r['status_code'] == 403]
            
            if accessible_services:
                print("  Accessible services:")
                for service in accessible_services:
                    print(f"    ✅ {service['description']}: {service['url']}")
            
            if auth_required_services:
                print("  Services requiring authentication:")
                for service in auth_required_services:
                    print(f"    🔒 {service['description']}: {service['url']}")
        
        # Save results
        output = {
            'found_projects': found_projects,
            'all_results': all_results,
            'search_summary': {
                'total_projects_tested': len(project_variations),
                'projects_found': len(found_projects),
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        with open('firebase_project_search_results.json', 'w') as f:
            json.dump(output, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: firebase_project_search_results.json")
        
    else:
        print("\n❌ No existing Firebase projects found with the tested names.")
        print("\nThis could mean:")
        print("- The project uses a completely different naming pattern")
        print("- The project is private/internal with a non-obvious name")
        print("- The app uses a different backend service")
        print("- The project name contains numbers or special characters")

if __name__ == "__main__":
    main()
