#!/usr/bin/env python3
"""
<PERSON><PERSON> Letters Downloader
Downloads letters from RebbeResponsa.app using the discovered URL patterns
"""

import requests
import json
import time
import os
from datetime import datetime
import re

def get_letter(year, letter_id=None):
    """Get a specific letter or year listing"""
    
    if letter_id:
        url = f"https://RebbeResponsa.app/letters/{year}/{letter_id}"
    else:
        url = f"https://RebbeResponsa.app/letters/{year}"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        
        return {
            'url': url,
            'year': year,
            'letter_id': letter_id,
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'content': response.text,
            'content_length': len(response.content)
        }
        
    except Exception as e:
        return {
            'url': url,
            'year': year,
            'letter_id': letter_id,
            'status_code': 'ERROR',
            'success': False,
            'content': '',
            'error': str(e)
        }

def test_known_letters():
    """Test the specific letters you provided"""
    
    known_letters = [
        ('5744', '23455'),  # Original
        ('5711', '29797'),  # From your list
        ('5711', '20587'),
        ('5711', '20586'),
        ('5716', '20745'),
        ('5743', '23338'),
    ]
    
    print("🔍 Testing known letter URLs...")
    print("-" * 50)
    
    results = []
    
    for year, letter_id in known_letters:
        print(f"Testing: {year}/{letter_id}")
        
        result = get_letter(year, letter_id)
        results.append(result)
        
        if result['success']:
            print(f"  ✅ SUCCESS: {result['content_length']} bytes")
            
            # Extract letter content preview
            content = result['content']
            
            # Look for letter text (try to find main content)
            if 'rebbe' in content.lower() or 'letter' in content.lower():
                # Try to extract meaningful text
                text_preview = re.sub(r'<[^>]+>', '', content)  # Remove HTML tags
                text_preview = ' '.join(text_preview.split())  # Clean whitespace
                print(f"  📄 Preview: {text_preview[:150]}...")
            
        else:
            print(f"  ❌ {result['status_code']}: {result.get('error', 'Failed')}")
        
        time.sleep(1)  # Be respectful
    
    return results

def test_year_listings():
    """Test if year-only URLs give us letter listings"""
    
    years_to_test = ['5711', '5716', '5743', '5744']
    
    print("\n🔍 Testing year listing URLs...")
    print("-" * 50)
    
    year_results = []
    
    for year in years_to_test:
        print(f"Testing year listing: {year}")
        
        result = get_letter(year)
        year_results.append(result)
        
        if result['success']:
            print(f"  ✅ SUCCESS: {result['content_length']} bytes")
            
            # Look for letter IDs in the response
            content = result['content']
            
            # Try to find letter ID patterns
            letter_ids = re.findall(r'/letters/' + year + r'/(\d+)', content)
            if letter_ids:
                print(f"  📋 Found {len(letter_ids)} letter IDs: {letter_ids[:5]}...")
                
                # Save the IDs for this year
                result['letter_ids'] = letter_ids
            else:
                print(f"  📄 No letter IDs found in response")
                
        else:
            print(f"  ❌ {result['status_code']}: {result.get('error', 'Failed')}")
        
        time.sleep(1)
    
    return year_results

def discover_id_ranges():
    """Try to discover the ID ranges for each year"""
    
    print("\n🔍 Discovering ID ranges...")
    print("-" * 50)
    
    # Based on your examples, try ranges around known IDs
    test_ranges = [
        ('5711', [20580, 20590, 29790, 29800]),  # Around known IDs
        ('5716', [20740, 20750]),
        ('5743', [23330, 23340]),
        ('5744', [23450, 23460]),
    ]
    
    range_results = []
    
    for year, id_list in test_ranges:
        print(f"\nTesting ID range for year {year}:")
        
        for letter_id in id_list:
            result = get_letter(year, str(letter_id))
            
            if result['success']:
                print(f"  ✅ {letter_id}: Found letter")
                range_results.append(result)
            else:
                print(f"  ❌ {letter_id}: Not found")
            
            time.sleep(0.5)
    
    return range_results

def extract_letter_content(html_content):
    """Extract the actual letter text from HTML"""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', html_content)
    
    # Clean up whitespace
    text = ' '.join(text.split())
    
    # Try to find the main letter content
    # Look for common patterns in Rebbe's letters
    patterns = [
        r'(?i)(dear|to|regarding|in response|your letter|you write|you ask).*',
        r'(?i)(ב"ה|בעזהי"ת).*',  # Hebrew headers
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            return match.group(0)[:1000]  # Return first 1000 chars
    
    # If no pattern found, return first part of cleaned text
    return text[:1000] if text else "No content extracted"

def main():
    print("Rebbe Letters Downloader")
    print("=" * 60)
    print("Testing the letter URLs you provided...")
    print()
    
    # Test known letters
    letter_results = test_known_letters()
    
    # Test year listings
    year_results = test_year_listings()
    
    # Discover ID ranges
    range_results = discover_id_ranges()
    
    # Analyze results
    print("\n" + "=" * 60)
    print("ANALYSIS RESULTS")
    print("=" * 60)
    
    successful_letters = [r for r in letter_results + range_results if r['success']]
    successful_years = [r for r in year_results if r['success']]
    
    print(f"\n✅ Successfully accessed {len(successful_letters)} individual letters")
    print(f"✅ Successfully accessed {len(successful_years)} year listings")
    
    # Show letter content samples
    if successful_letters:
        print(f"\n📝 Letter Content Samples:")
        for i, letter in enumerate(successful_letters[:3], 1):
            content = extract_letter_content(letter['content'])
            print(f"\n{i}. Year {letter['year']}, ID {letter['letter_id']}:")
            print(f"   {content[:200]}...")
    
    # Show discovered letter IDs
    total_discovered_ids = 0
    for year_result in successful_years:
        if 'letter_ids' in year_result:
            total_discovered_ids += len(year_result['letter_ids'])
            print(f"\n📋 Year {year_result['year']}: {len(year_result['letter_ids'])} letters found")
    
    if total_discovered_ids > 0:
        print(f"\n🎯 TOTAL DISCOVERED: {total_discovered_ids} letter IDs!")
    
    # Save results
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'letter_tests': letter_results,
        'year_tests': year_results,
        'range_tests': range_results,
        'summary': {
            'successful_letters': len(successful_letters),
            'successful_years': len(successful_years),
            'total_discovered_ids': total_discovered_ids
        }
    }
    
    with open('rebbe_letters_analysis.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n💾 Results saved to: rebbe_letters_analysis.json")
    
    # Next steps
    if successful_letters or total_discovered_ids > 0:
        print(f"\n🚀 NEXT STEPS:")
        print("We can now create a script to:")
        print("1. Download all discovered letter IDs")
        print("2. Systematically scan for more letters")
        print("3. Extract and save the letter content")
        print("4. Build a complete database of all 5000+ letters")

if __name__ == "__main__":
    main()
