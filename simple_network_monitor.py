#!/usr/bin/env python3
"""
Simple Network Monitor
Monitors network connections to identify Firebase endpoints
"""

import subprocess
import time
import re
import json
from datetime import datetime

def get_network_connections():
    """Get current network connections using netstat"""
    try:
        # Run netstat to get network connections
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        return result.stdout
    except Exception as e:
        print(f"Error running netstat: {e}")
        return ""

def extract_firebase_connections(netstat_output):
    """Extract Firebase-related connections from netstat output"""
    firebase_patterns = [
        r'firebasestorage\.googleapis\.com',
        r'cloudfunctions\.net',
        r'firebaseio\.com',
        r'firestore\.googleapis\.com',
        r'googleapis\.com'
    ]
    
    connections = []
    lines = netstat_output.split('\n')
    
    for line in lines:
        for pattern in firebase_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                connections.append(line.strip())
                break
    
    return connections

def monitor_network(duration_seconds=60):
    """Monitor network connections for a specified duration"""
    print(f"Monitoring network connections for {duration_seconds} seconds...")
    print("Please use the Rebbe Responsa app now to browse letters.")
    print("-" * 50)
    
    all_connections = set()
    start_time = time.time()
    
    while time.time() - start_time < duration_seconds:
        netstat_output = get_network_connections()
        firebase_connections = extract_firebase_connections(netstat_output)
        
        for conn in firebase_connections:
            if conn not in all_connections:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] NEW: {conn}")
                all_connections.add(conn)
        
        time.sleep(2)  # Check every 2 seconds
    
    return list(all_connections)

def analyze_connections(connections):
    """Analyze the captured connections to extract useful information"""
    analysis = {
        'firebase_storage': [],
        'cloud_functions': [],
        'firestore': [],
        'other_google': []
    }
    
    for conn in connections:
        if 'firebasestorage.googleapis.com' in conn:
            analysis['firebase_storage'].append(conn)
        elif 'cloudfunctions.net' in conn:
            analysis['cloud_functions'].append(conn)
        elif 'firestore.googleapis.com' in conn:
            analysis['firestore'].append(conn)
        elif 'googleapis.com' in conn:
            analysis['other_google'].append(conn)
    
    return analysis

def main():
    print("Simple Network Monitor for Firebase Connections")
    print("=" * 60)
    print()
    print("This tool will monitor your network connections while you use the app.")
    print("It will look for Firebase-related connections that might reveal the")
    print("endpoints where the letters are stored.")
    print()
    
    # Ask user for monitoring duration
    try:
        duration = int(input("How many seconds to monitor? (default: 60): ") or "60")
    except ValueError:
        duration = 60
    
    print()
    print("INSTRUCTIONS:")
    print("1. Keep this window open")
    print("2. Open the Rebbe Responsa app on your device")
    print("3. Browse through different letters")
    print("4. Search for letters")
    print("5. Try to access as many letters as possible")
    print()
    input("Press Enter when ready to start monitoring...")
    print()
    
    # Monitor network connections
    connections = monitor_network(duration)
    
    print("\n" + "=" * 60)
    print("MONITORING COMPLETE")
    print("=" * 60)
    
    if connections:
        print(f"Found {len(connections)} Firebase-related connections:")
        print()
        
        # Analyze connections
        analysis = analyze_connections(connections)
        
        for category, conns in analysis.items():
            if conns:
                print(f"{category.upper().replace('_', ' ')}:")
                for conn in conns:
                    print(f"  {conn}")
                print()
        
        # Save results
        results = {
            'timestamp': datetime.now().isoformat(),
            'duration_seconds': duration,
            'total_connections': len(connections),
            'connections': connections,
            'analysis': analysis
        }
        
        with open('network_monitoring_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved to: network_monitoring_results.json")
        
        # Extract potential project IDs
        project_ids = set()
        for conn in connections:
            # Look for patterns like project-id.cloudfunctions.net
            match = re.search(r'([a-zA-Z0-9-]+)\.cloudfunctions\.net', conn)
            if match:
                project_ids.add(match.group(1))
            
            # Look for patterns in Firebase Storage URLs
            match = re.search(r'firebasestorage\.googleapis\.com.*?([a-zA-Z0-9-]+)\.appspot\.com', conn)
            if match:
                project_ids.add(match.group(1))
        
        if project_ids:
            print("\nPOTENTIAL FIREBASE PROJECT IDs:")
            for project_id in project_ids:
                print(f"  {project_id}")
        
    else:
        print("No Firebase-related connections found.")
        print()
        print("This could mean:")
        print("- The app wasn't used during monitoring")
        print("- The app uses different endpoints")
        print("- The connections were too brief to capture")
        print("- The app caches data locally")

if __name__ == "__main__":
    main()
