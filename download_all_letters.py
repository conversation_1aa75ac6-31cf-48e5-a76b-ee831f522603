#!/usr/bin/env python3
"""
Download All Rebbe Letters
Downloads the complete list and content of all letters from Firebase Storage
"""

import requests
import json
import time
import os
from datetime import datetime
from urllib.parse import unquote

# Firebase configuration
FIREBASE_CONFIG = {
    'apiKey': 'AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM',
    'projectId': 'rebberesponsa',
    'storageBucket': 'english-letters.appspot.com'
}

def get_all_files_from_storage():
    """Get complete list of all files in Firebase Storage"""
    
    api_key = FIREBASE_CONFIG['apiKey']
    storage_bucket = FIREBASE_CONFIG['storageBucket']
    
    base_url = f"https://firebasestorage.googleapis.com/v0/b/{storage_bucket}/o"
    
    all_items = []
    next_page_token = None
    page_count = 0
    
    print("📥 Downloading complete file list from Firebase Storage...")
    print("-" * 60)
    
    while True:
        page_count += 1
        
        # Build URL with pagination
        url = f"{base_url}?key={api_key}"
        if next_page_token:
            url += f"&pageToken={next_page_token}"
        
        print(f"📄 Fetching page {page_count}...")
        
        try:
            response = requests.get(url, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ Error: Status {response.status_code}")
                break
            
            data = response.json()
            
            # Add items from this page
            if 'items' in data:
                items = data['items']
                all_items.extend(items)
                print(f"   Found {len(items)} items (Total: {len(all_items)})")
            
            # Check for next page
            if 'nextPageToken' in data:
                next_page_token = data['nextPageToken']
                print(f"   Next page token: {next_page_token[:20]}...")
            else:
                print(f"   ✅ Reached end of list")
                break
                
        except Exception as e:
            print(f"❌ Error fetching page {page_count}: {e}")
            break
        
        time.sleep(0.5)  # Be respectful
    
    print(f"\n🎯 TOTAL FILES FOUND: {len(all_items)}")
    return all_items

def analyze_file_structure(items):
    """Analyze the structure of files to understand organization"""
    
    print("\n🔍 Analyzing file structure...")
    print("-" * 40)
    
    # Group files by type/pattern
    file_patterns = {}
    letter_files = []
    
    for item in items:
        name = item.get('name', '')
        
        # Decode URL-encoded names
        decoded_name = unquote(name)
        
        # Look for letter patterns
        if any(keyword in decoded_name.lower() for keyword in ['letter', 'responsa', 'igrot', 'year']):
            letter_files.append({
                'name': decoded_name,
                'original_name': name,
                'size': item.get('size', 0),
                'download_url': f"https://firebasestorage.googleapis.com/v0/b/{FIREBASE_CONFIG['storageBucket']}/o/{name}?alt=media&token={FIREBASE_CONFIG['apiKey']}"
            })
        
        # Categorize by file extension or pattern
        if '.' in decoded_name:
            ext = decoded_name.split('.')[-1].lower()
            if ext not in file_patterns:
                file_patterns[ext] = []
            file_patterns[ext].append(decoded_name)
        else:
            # Files without extension
            if 'no_extension' not in file_patterns:
                file_patterns['no_extension'] = []
            file_patterns['no_extension'].append(decoded_name)
    
    # Show analysis
    print(f"📊 File type breakdown:")
    for ext, files in file_patterns.items():
        print(f"   {ext}: {len(files)} files")
        if len(files) <= 5:
            for file in files:
                print(f"      {file}")
        else:
            for file in files[:3]:
                print(f"      {file}")
            print(f"      ... and {len(files)-3} more")
    
    print(f"\n📝 Letter files found: {len(letter_files)}")
    
    return letter_files, file_patterns

def download_sample_letters(letter_files, max_samples=10):
    """Download a sample of letters to examine content"""
    
    print(f"\n📥 Downloading sample letters...")
    print("-" * 40)
    
    sample_letters = []
    
    for i, letter_file in enumerate(letter_files[:max_samples]):
        print(f"📄 Downloading: {letter_file['name']}")
        
        try:
            response = requests.get(letter_file['download_url'], timeout=30)
            
            if response.status_code == 200:
                content = response.text
                
                sample_letters.append({
                    'name': letter_file['name'],
                    'size': len(content),
                    'content': content,
                    'preview': content[:300] + "..." if len(content) > 300 else content
                })
                
                print(f"   ✅ Downloaded: {len(content)} bytes")
                print(f"   Preview: {content[:100]}...")
                
            else:
                print(f"   ❌ Error: Status {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        time.sleep(0.5)
    
    return sample_letters

def search_for_specific_letters(items):
    """Search for the specific letter IDs we know about"""
    
    known_letter_ids = ['23455', '29797', '20586', '20745', '23338']
    
    print(f"\n🔍 Searching for known letter IDs...")
    print("-" * 40)
    
    found_letters = []
    
    for item in items:
        name = item.get('name', '')
        decoded_name = unquote(name)
        
        # Check if any known letter ID is in the filename
        for letter_id in known_letter_ids:
            if letter_id in decoded_name:
                download_url = f"https://firebasestorage.googleapis.com/v0/b/{FIREBASE_CONFIG['storageBucket']}/o/{name}?alt=media&token={FIREBASE_CONFIG['apiKey']}"
                
                found_letters.append({
                    'letter_id': letter_id,
                    'name': decoded_name,
                    'original_name': name,
                    'size': item.get('size', 0),
                    'download_url': download_url
                })
                
                print(f"✅ Found letter {letter_id}: {decoded_name}")
    
    return found_letters

def main():
    print("Download All Rebbe Letters")
    print("=" * 60)
    print("Accessing Firebase Storage to download all letters...")
    print()
    
    # Get complete file list
    all_items = get_all_files_from_storage()
    
    if not all_items:
        print("❌ No files found in storage")
        return
    
    # Analyze file structure
    letter_files, file_patterns = analyze_file_structure(all_items)
    
    # Search for specific letters we know about
    found_specific_letters = search_for_specific_letters(all_items)
    
    # Download sample letters
    sample_letters = download_sample_letters(letter_files if letter_files else all_items[:10])
    
    # Summary
    print("\n" + "=" * 60)
    print("DOWNLOAD SUMMARY")
    print("=" * 60)
    
    print(f"📊 Total files in storage: {len(all_items)}")
    print(f"📝 Letter files identified: {len(letter_files)}")
    print(f"🎯 Known letters found: {len(found_specific_letters)}")
    print(f"📥 Sample letters downloaded: {len(sample_letters)}")
    
    if found_specific_letters:
        print(f"\n✅ Found specific letters:")
        for letter in found_specific_letters:
            print(f"   📄 Letter {letter['letter_id']}: {letter['name']}")
    
    if sample_letters:
        print(f"\n📄 Sample letter content:")
        for letter in sample_letters[:3]:
            print(f"\n   📝 {letter['name']}:")
            print(f"      Size: {letter['size']} bytes")
            print(f"      Preview: {letter['preview']}")
    
    # Save all results
    results = {
        'timestamp': datetime.now().isoformat(),
        'total_files': len(all_items),
        'all_items': all_items,
        'letter_files': letter_files,
        'file_patterns': file_patterns,
        'found_specific_letters': found_specific_letters,
        'sample_letters': sample_letters
    }
    
    with open('all_letters_download.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Complete results saved to: all_letters_download.json")
    
    if len(all_items) > 1000:
        print(f"\n🎯 SUCCESS! Found {len(all_items)} files - this appears to be the complete letter database!")
        print("Next steps:")
        print("1. Download all letter content")
        print("2. Parse and organize by year/topic")
        print("3. Create searchable database")
        print("4. Extract insights and quotes")

if __name__ == "__main__":
    main()
