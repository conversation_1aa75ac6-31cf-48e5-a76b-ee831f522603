#!/usr/bin/env python3
"""
Rebbe Responsa API Access
Access letters directly from RebbeResponsa.app
"""

import requests
import json
import time
import os
from datetime import datetime

def test_api_endpoint(url):
    """Test an API endpoint and return the response"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/html, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        
        return {
            'url': url,
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'content': response.text,
            'content_type': response.headers.get('content-type', ''),
            'content_length': len(response.content)
        }
        
    except Exception as e:
        return {
            'url': url,
            'status_code': 'ERROR',
            'success': False,
            'content': '',
            'error': str(e)
        }

def discover_api_structure():
    """Discover the API structure of RebbeResponsa.app"""
    
    base_url = "https://RebbeResponsa.app"
    
    # Test various endpoints
    test_urls = [
        f"{base_url}",
        f"{base_url}/api",
        f"{base_url}/api/letters",
        f"{base_url}/letters",
        f"{base_url}/letters/5744/23455",  # The example you provided
        f"{base_url}/api/letters/5744/23455",
        f"{base_url}/api/years",
        f"{base_url}/api/search",
        f"{base_url}/data",
        f"{base_url}/data/letters",
        f"{base_url}/.well-known/api"
    ]
    
    print("🔍 Discovering API structure...")
    print("-" * 50)
    
    results = []
    
    for url in test_urls:
        print(f"Testing: {url}")
        result = test_api_endpoint(url)
        results.append(result)
        
        if result['success']:
            print(f"  ✅ SUCCESS: {result['content_length']} bytes")
            
            # Check if it's JSON
            try:
                json_data = json.loads(result['content'])
                print(f"  📄 JSON Response: {str(json_data)[:100]}...")
            except:
                print(f"  📄 HTML/Text Response: {result['content'][:100]}...")
        else:
            print(f"  ❌ {result['status_code']}: {result.get('error', 'Failed')}")
        
        time.sleep(1)  # Be respectful
    
    return results

def try_letter_ranges():
    """Try to access letters from different years and ID ranges"""
    
    base_url = "https://RebbeResponsa.app"
    
    # Hebrew years when the Rebbe wrote letters (approximately 5710-5754)
    years_to_try = ['5710', '5720', '5730', '5740', '5744', '5750', '5754']
    
    # Try different ID patterns
    id_patterns = [
        lambda year: [f"{year}/1", f"{year}/100", f"{year}/1000", f"{year}/10000"],  # Simple IDs
        lambda year: [f"{year}/23455"],  # The known working ID
        lambda year: [f"{year}/12345", f"{year}/54321"],  # Common test IDs
    ]
    
    print("\n🔍 Testing letter access patterns...")
    print("-" * 50)
    
    successful_letters = []
    
    for year in years_to_try:
        print(f"\nTesting year {year}:")
        
        for pattern_func in id_patterns:
            ids = pattern_func(year)
            
            for letter_id in ids:
                url = f"{base_url}/letters/{letter_id}"
                print(f"  Testing: {url}")
                
                result = test_api_endpoint(url)
                
                if result['success']:
                    print(f"    ✅ SUCCESS: Found letter!")
                    successful_letters.append({
                        'url': url,
                        'year': year,
                        'id': letter_id,
                        'content': result['content'][:500] + "..." if len(result['content']) > 500 else result['content']
                    })
                else:
                    print(f"    ❌ {result['status_code']}")
                
                time.sleep(0.5)
    
    return successful_letters

def try_api_endpoints():
    """Try to find API endpoints that might list all letters"""
    
    base_url = "https://RebbeResponsa.app"
    
    # Common API patterns
    api_endpoints = [
        "/api/letters/all",
        "/api/letters/list",
        "/api/letters/index",
        "/api/v1/letters",
        "/api/v2/letters",
        "/letters/all",
        "/letters/list",
        "/letters/index",
        "/data/letters.json",
        "/data/all-letters.json",
        "/export/letters",
        "/search/all"
    ]
    
    print("\n🔍 Testing API endpoints...")
    print("-" * 50)
    
    api_results = []
    
    for endpoint in api_endpoints:
        url = f"{base_url}{endpoint}"
        print(f"Testing: {url}")
        
        result = test_api_endpoint(url)
        
        if result['success']:
            print(f"  ✅ SUCCESS: {result['content_length']} bytes")
            api_results.append(result)
            
            # Try to parse as JSON
            try:
                json_data = json.loads(result['content'])
                if isinstance(json_data, list):
                    print(f"    📋 Found list with {len(json_data)} items")
                elif isinstance(json_data, dict):
                    print(f"    📄 Found object with keys: {list(json_data.keys())[:5]}")
            except:
                print(f"    📄 Non-JSON response")
        else:
            print(f"  ❌ {result['status_code']}")
        
        time.sleep(0.5)
    
    return api_results

def main():
    print("Rebbe Responsa API Access Tool")
    print("=" * 60)
    print("Based on the URL: https://RebbeResponsa.app/letters/5744/23455")
    print("Attempting to discover the API structure and access letters...")
    print()
    
    # Step 1: Discover API structure
    api_structure = discover_api_structure()
    
    # Step 2: Try to access specific letters
    letter_samples = try_letter_ranges()
    
    # Step 3: Try to find bulk API endpoints
    api_endpoints = try_api_endpoints()
    
    # Summary
    print("\n" + "=" * 60)
    print("DISCOVERY RESULTS")
    print("=" * 60)
    
    # Successful API calls
    successful_calls = [r for r in api_structure + api_endpoints if r['success']]
    
    if successful_calls:
        print(f"\n✅ Found {len(successful_calls)} working endpoints:")
        for call in successful_calls:
            print(f"  📍 {call['url']} ({call['content_length']} bytes)")
    
    # Successful letter access
    if letter_samples:
        print(f"\n📝 Found {len(letter_samples)} accessible letters:")
        for letter in letter_samples:
            print(f"  📄 {letter['url']}")
            print(f"     Preview: {letter['content'][:100]}...")
    
    # Save all results
    results = {
        'timestamp': datetime.now().isoformat(),
        'base_url': 'https://RebbeResponsa.app',
        'api_structure_tests': api_structure,
        'letter_samples': letter_samples,
        'api_endpoint_tests': api_endpoints,
        'summary': {
            'working_endpoints': len(successful_calls),
            'accessible_letters': len(letter_samples),
            'total_tests': len(api_structure) + len(api_endpoints)
        }
    }
    
    with open('rebbe_responsa_api_discovery.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Full results saved to: rebbe_responsa_api_discovery.json")
    
    # Next steps
    if successful_calls or letter_samples:
        print(f"\n🎯 NEXT STEPS:")
        print("Based on the working endpoints found, we can now:")
        print("1. Access individual letters using the URL pattern")
        print("2. Try to find bulk download endpoints")
        print("3. Systematically download all 5000+ letters")
    else:
        print(f"\n❌ No working endpoints found.")
        print("The website might:")
        print("- Require authentication")
        print("- Use different URL patterns")
        print("- Have rate limiting or bot protection")

if __name__ == "__main__":
    main()
