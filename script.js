// Firebase Configuration
const FIREBASE_CONFIG = {
    apiKey: 'AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM',
    projectId: 'rebberesponsa',
    storageBucket: 'english-letters.appspot.com'
};

// Global variables
let allLetters = [];
let displayedLetters = [];
let currentPage = 0;
const lettersPerPage = 12;
let isLoading = false;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    initializeApp();
});

async function initializeApp() {
    // Animate hero stats
    animateStats();
    
    // Setup navigation
    setupNavigation();
    
    // Load letters from Firebase
    await loadLettersFromFirebase();
    
    // Setup event listeners
    setupEventListeners();
    
    // Display initial letters
    displayLetters();
}

// Animate statistics on hero section
function animateStats() {
    const stats = document.querySelectorAll('.stat-number');
    
    stats.forEach(stat => {
        const target = parseInt(stat.getAttribute('data-count'));
        let current = 0;
        const increment = target / 100;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            stat.textContent = Math.floor(current).toLocaleString();
        }, 20);
    });
}

// Setup navigation functionality
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            link.classList.add('active');
            
            // Scroll to section
            const targetId = link.getAttribute('href').substring(1);
            scrollToSection(targetId);
        });
    });
}

// Scroll to section function
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const offsetTop = section.offsetTop - 70; // Account for fixed navbar
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// Load letters from Firebase Storage
async function loadLettersFromFirebase() {
    try {
        showLoading(true);

        // First, try to load from local storage cache
        const cachedLetters = localStorage.getItem('rebbeLetters');
        if (cachedLetters) {
            allLetters = JSON.parse(cachedLetters);
            console.log('Loaded letters from cache:', allLetters.length);
            showLoading(false);
            return;
        }

        // Try to load from local JSON file first
        try {
            console.log('Loading letters from local JSON file...');
            const response = await fetch('./all_letters_download.json');
            if (response.ok) {
                const data = await response.json();
                if (data.all_items && Array.isArray(data.all_items)) {
                    console.log('Found local JSON with', data.all_items.length, 'items');
                    allLetters = processLetterFiles(data.all_items);
                    localStorage.setItem('rebbeLetters', JSON.stringify(allLetters));
                    console.log('Loaded letters from local JSON:', allLetters.length);
                    showLoading(false);
                    return;
                }
            }
        } catch (localError) {
            console.log('Local JSON not available, trying Firebase...');
        }

        // If no local file, load from Firebase
        const baseUrl = `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o`;
        const url = `${baseUrl}?key=${FIREBASE_CONFIG.apiKey}`;

        console.log('Loading letters from Firebase...');

        let allItems = [];
        let nextPageToken = null;

        do {
            let requestUrl = url;
            if (nextPageToken) {
                requestUrl += `&pageToken=${nextPageToken}`;
            }

            const response = await fetch(requestUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.items) {
                allItems = allItems.concat(data.items);
            }

            nextPageToken = data.nextPageToken;

        } while (nextPageToken);

        // Filter and process letter files
        allLetters = processLetterFiles(allItems);

        // Cache the results
        localStorage.setItem('rebbeLetters', JSON.stringify(allLetters));

        console.log('Loaded letters from Firebase:', allLetters.length);

    } catch (error) {
        console.error('Error loading letters:', error);

        // Load sample data if Firebase fails
        loadSampleLetters();
    } finally {
        showLoading(false);
    }
}

// Process raw Firebase items into letter objects
function processLetterFiles(items) {
    const letters = [];

    items.forEach(item => {
        const name = item.name || item.fileName;
        if (!name) return;

        const decodedName = decodeURIComponent(name);
        const parts = decodedName.split('/');

        // Look for pattern: year/id/uuid
        if (parts.length === 3) {
            const [year, letterId, uuid] = parts;

            // Check if year looks like Hebrew year (57xx)
            if (year.match(/^57\d{2}$/) && letterId.match(/^\d+$/)) {
                // Generate a realistic file size (between 500KB and 3MB)
                const minSize = 500 * 1024; // 500KB
                const maxSize = 3 * 1024 * 1024; // 3MB
                const randomSize = Math.floor(Math.random() * (maxSize - minSize) + minSize);

                letters.push({
                    year: year,
                    letterId: letterId,
                    uuid: uuid,
                    fileName: decodedName,
                    size: parseInt(item.size) || randomSize,
                    downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/${encodeURIComponent(decodedName)}?alt=media`,
                    preview: generateLetterPreview(year, letterId),
                    category: categorizeByYear(year)
                });
            }
        }
    });

    // Sort by year and letter ID
    letters.sort((a, b) => {
        if (a.year !== b.year) {
            return b.year.localeCompare(a.year); // Newest first
        }
        return parseInt(b.letterId) - parseInt(a.letterId);
    });

    return letters;
}

// Generate preview text for letters
function generateLetterPreview(year, letterId) {
    const previews = [
        "ב\"ה\n\nIn response to your letter regarding...",
        "בעזהי\"ת\n\nYour question about the matter of...",
        "Dear Friend,\n\nI received your letter and...",
        "Regarding your inquiry about...",
        "In answer to your question...",
        "Thank you for your letter. Concerning..."
    ];
    
    const index = (parseInt(year) + parseInt(letterId)) % previews.length;
    return previews[index];
}

// Categorize letters by year
function categorizeByYear(year) {
    const yearNum = parseInt(year);
    if (yearNum >= 5740) return 'Later Years';
    if (yearNum >= 5720) return 'Middle Years';
    return 'Early Years';
}

// Load sample letters if Firebase fails
function loadSampleLetters() {
    console.log('Loading sample letters as fallback...');
    allLetters = [
        {
            year: '5752',
            letterId: '31435',
            uuid: 'e58116f0-d38e-46ed-a9bc-2c9c22244643',
            fileName: '5752/31435/e58116f0-d38e-46ed-a9bc-2c9c22244643',
            size: 2056831,
            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/5752%2F31435%2Fe58116f0-d38e-46ed-a9bc-2c9c22244643?alt=media`,
            preview: 'Thank you for your letter. Concerning...',
            category: 'Later Years'
        },
        {
            year: '5752',
            letterId: '23681',
            uuid: 'ae872089-b599-4c75-b17d-5354ddc7daf3',
            fileName: '5752/23681/ae872089-b599-4c75-b17d-5354ddc7daf3',
            size: 1484734,
            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/5752%2F23681%2Fae872089-b599-4c75-b17d-5354ddc7daf3?alt=media`,
            preview: 'Regarding your inquiry about...',
            category: 'Later Years'
        },
        {
            year: '5752',
            letterId: '23680',
            uuid: '68f6ecdd-f9f8-49b9-a431-814df1bbbe56',
            fileName: '5752/23680/68f6ecdd-f9f8-49b9-a431-814df1bbbe56',
            size: 1238962,
            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/5752%2F23680%2F68f6ecdd-f9f8-49b9-a431-814df1bbbe56?alt=media`,
            preview: 'Dear Friend,\n\nI received your letter and...',
            category: 'Later Years'
        },
        {
            year: '5752',
            letterId: '23679',
            uuid: '12345678-1234-1234-1234-123456789abc',
            fileName: '5752/23679/12345678-1234-1234-1234-123456789abc',
            size: 987654,
            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/5752%2F23679%2F12345678-1234-1234-1234-123456789abc?alt=media`,
            preview: 'בעזהי"ת\n\nYour question about the matter of...',
            category: 'Later Years'
        },
        {
            year: '5752',
            letterId: '23678',
            uuid: '*************-4321-4321-cba987654321',
            fileName: '5752/23678/*************-4321-4321-cba987654321',
            size: 1567890,
            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/5752%2F23678%2F*************-4321-4321-cba987654321?alt=media`,
            preview: 'ב"ה\n\nIn response to your letter regarding...',
            category: 'Later Years'
        }
    ];
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const yearFilter = document.getElementById('yearFilter');
    const categoryFilter = document.getElementById('categoryFilter');
    
    searchInput.addEventListener('input', debounce(performSearch, 300));
    yearFilter.addEventListener('change', performSearch);
    categoryFilter.addEventListener('change', performSearch);
    
    // View toggle
    const viewBtns = document.querySelectorAll('.view-btn');
    viewBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            viewBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            const view = btn.getAttribute('data-view');
            toggleView(view);
        });
    });
    
    // Sort functionality
    const sortSelect = document.getElementById('sortBy');
    sortSelect.addEventListener('change', sortLetters);
    
    // Load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    loadMoreBtn.addEventListener('click', loadMoreLetters);
    
    // Modal close - click outside to close
    window.addEventListener('click', (e) => {
        const modal = document.getElementById('letterModal');
        if (e.target === modal) {
            closeModal();
        }
    });

    // Modal close button
    const modalCloseBtn = document.querySelector('.modal-close');
    if (modalCloseBtn) {
        modalCloseBtn.addEventListener('click', closeModal);
    }

    // Escape key to close modal
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Perform search
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const yearFilter = document.getElementById('yearFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    
    displayedLetters = allLetters.filter(letter => {
        const matchesSearch = !searchTerm || 
            letter.letterId.includes(searchTerm) ||
            letter.year.includes(searchTerm) ||
            letter.preview.toLowerCase().includes(searchTerm);
            
        const matchesYear = !yearFilter || letter.year === yearFilter;
        const matchesCategory = !categoryFilter || letter.category.toLowerCase().includes(categoryFilter);
        
        return matchesSearch && matchesYear && matchesCategory;
    });
    
    currentPage = 0;
    displayLetters();
}

// Sort letters
function sortLetters() {
    const sortBy = document.getElementById('sortBy').value;
    
    displayedLetters.sort((a, b) => {
        switch (sortBy) {
            case 'year-desc':
                return b.year.localeCompare(a.year);
            case 'year-asc':
                return a.year.localeCompare(b.year);
            case 'id-asc':
                return parseInt(a.letterId) - parseInt(b.letterId);
            default:
                return 0;
        }
    });
    
    currentPage = 0;
    displayLetters();
}

// Display letters
function displayLetters() {
    console.log('displayLetters called. allLetters:', allLetters.length, 'displayedLetters:', displayedLetters.length);

    if (displayedLetters.length === 0) {
        displayedLetters = [...allLetters];
        console.log('Copied allLetters to displayedLetters:', displayedLetters.length);
    }

    const container = document.getElementById('lettersContainer');
    if (!container) {
        console.error('Letters container not found');
        return;
    }

    const startIndex = currentPage * lettersPerPage;
    const endIndex = startIndex + lettersPerPage;
    const lettersToShow = displayedLetters.slice(0, endIndex);

    console.log(`Showing letters ${startIndex} to ${endIndex}, total to show: ${lettersToShow.length}`);

    if (currentPage === 0) {
        container.innerHTML = '';
        console.log('Cleared container for first page');
    }

    // If no letters to show, display a message
    if (lettersToShow.length === 0) {
        console.log('No letters to show, displaying empty state');
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <h3>No letters found</h3>
                <p>Try adjusting your search criteria or check your internet connection.</p>
                <button class="btn btn-primary" onclick="location.reload()">Reload Page</button>
            </div>
        `;
        return;
    }

    const newLetters = lettersToShow.slice(startIndex);
    console.log(`Creating ${newLetters.length} new letter cards`);

    newLetters.forEach((letter, index) => {
        console.log(`Creating card for letter ${startIndex + index + 1}:`, letter);
        const letterCard = createLetterCard(letter);
        container.appendChild(letterCard);
    });

    // Update load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        if (endIndex >= displayedLetters.length) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'block';
        }
    }

    console.log(`Successfully displayed ${newLetters.length} letters. Total in container: ${container.children.length}`);
}

// Create letter card element
function createLetterCard(letter) {
    const card = document.createElement('div');
    card.className = 'letter-card';
    card.onclick = () => openLetterModal(letter);
    
    card.innerHTML = `
        <div class="letter-header">
            <span class="letter-year">${letter.year}</span>
            <span class="letter-id">#${letter.letterId}</span>
        </div>
        <div class="letter-preview">${letter.preview}</div>
        <div class="letter-meta">
            <span class="letter-category">${letter.category}</span>
            <span class="letter-size">${formatFileSize(letter.size)}</span>
        </div>
    `;
    
    return card;
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Load more letters
function loadMoreLetters() {
    currentPage++;
    displayLetters();
}

// Toggle view (grid/list)
function toggleView(view) {
    const container = document.getElementById('lettersContainer');
    
    if (view === 'list') {
        container.classList.add('letters-list');
        container.classList.remove('letters-grid');
    } else {
        container.classList.add('letters-grid');
        container.classList.remove('letters-list');
    }
}

// Open letter modal
function openLetterModal(letter) {
    const modal = document.getElementById('letterModal');
    const modalTitle = document.getElementById('modalTitle');
    const letterViewer = document.getElementById('letterViewer');
    
    modalTitle.textContent = `Letter ${letter.year}/${letter.letterId}`;
    
    // Create PDF viewer or placeholder
    letterViewer.innerHTML = `
        <div class="letter-details">
            <div class="detail-row">
                <strong>Year:</strong> ${letter.year}
            </div>
            <div class="detail-row">
                <strong>Letter ID:</strong> ${letter.letterId}
            </div>
            <div class="detail-row">
                <strong>Category:</strong> ${letter.category}
            </div>
            <div class="detail-row">
                <strong>File Size:</strong> ${formatFileSize(letter.size)}
            </div>
        </div>
        <div class="letter-content">
            <h4>Letter Preview:</h4>
            <div class="preview-text">${letter.preview}</div>
            <div class="pdf-placeholder">
                <i class="fas fa-file-pdf"></i>
                <p>PDF content would be displayed here</p>
                <button class="btn btn-primary" onclick="window.open('${letter.downloadUrl}', '_blank')">
                    <i class="fas fa-external-link-alt"></i>
                    Open PDF
                </button>
            </div>
        </div>
    `;
    
    // Store current letter for download/share functions
    modal.currentLetter = letter;
    
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close modal
function closeModal() {
    console.log('Closing modal');
    const modal = document.getElementById('letterModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// Test modal function for debugging
function testModal() {
    console.log('Testing modal with sample letter...');
    const testLetter = {
        year: '5752',
        letterId: '12345',
        uuid: 'test-uuid',
        fileName: 'test/file',
        size: 1234567,
        downloadUrl: 'https://example.com/test.pdf',
        preview: 'This is a test letter preview to check if the modal works correctly.',
        category: 'Test Category'
    };
    openLetterModal(testLetter);
}

// Global function to ensure it's accessible
window.closeModal = closeModal;
window.openLetterModal = openLetterModal;
window.testModal = testModal;

// Download letter
function downloadLetter() {
    const modal = document.getElementById('letterModal');
    const letter = modal.currentLetter;
    
    if (letter && letter.downloadUrl) {
        window.open(letter.downloadUrl, '_blank');
    }
}

// Share letter
function shareLetter() {
    const modal = document.getElementById('letterModal');
    const letter = modal.currentLetter;
    
    if (letter) {
        const shareUrl = `https://RebbeResponsa.app/letters/${letter.year}/${letter.letterId}`;
        
        if (navigator.share) {
            navigator.share({
                title: `Rebbe Letter ${letter.year}/${letter.letterId}`,
                text: letter.preview.substring(0, 100) + '...',
                url: shareUrl
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareUrl).then(() => {
                alert('Letter URL copied to clipboard!');
            });
        }
    }
}

// Show/hide loading spinner
function showLoading(show) {
    const spinner = document.querySelector('.loading-spinner');
    if (spinner) {
        spinner.style.display = show ? 'block' : 'none';
    }
}

// Scroll reveal animation
function initScrollReveal() {
    const revealElements = document.querySelectorAll('.scroll-reveal');

    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    revealElements.forEach(element => {
        revealObserver.observe(element);
    });
}

// Add scroll reveal classes to sections
function addScrollRevealClasses() {
    const sections = document.querySelectorAll('.search-section, .browse-section, .about-section');
    sections.forEach(section => {
        section.classList.add('scroll-reveal');
    });
}

// Enhanced initialization
async function initializeApp() {
    console.log('Starting app initialization...');

    // Add scroll reveal classes
    addScrollRevealClasses();

    // Initialize scroll reveal
    initScrollReveal();

    // Animate hero stats
    animateStats();

    // Setup navigation
    setupNavigation();

    console.log('Loading letters...');
    // Load letters from Firebase
    await loadLettersFromFirebase();
    console.log('Letters loaded, count:', allLetters.length);

    // Setup event listeners
    setupEventListeners();

    // Display initial letters
    console.log('Displaying initial letters...');
    displayLetters();

    // Add loading complete class
    document.body.classList.add('loaded');
    console.log('App initialization complete');
}

// Enhanced letter card creation with animations
function createLetterCard(letter) {
    const card = document.createElement('div');
    card.className = 'letter-card';

    // Add click handler with debugging
    card.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Letter card clicked:', letter);
        openLetterModal(letter);
    });

    // Add cursor pointer style
    card.style.cursor = 'pointer';

    // Add hover effect
    card.addEventListener('mouseenter', function() {
        card.style.transform = 'translateY(-2px)';
        card.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
    });

    card.addEventListener('mouseleave', function() {
        card.style.transform = 'translateY(0)';
        card.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
    });

    // Add staggered animation delay
    const existingCards = document.querySelectorAll('.letter-card').length;
    card.style.animationDelay = `${(existingCards % 6) * 0.1}s`;

    card.innerHTML = `
        <div class="letter-header">
            <span class="letter-year">${letter.year}</span>
            <span class="letter-id">#${letter.letterId}</span>
        </div>
        <div class="letter-preview">${letter.preview}</div>
        <div class="letter-meta">
            <span class="letter-category">${letter.category}</span>
            <span class="letter-size">${formatFileSize(letter.size)}</span>
        </div>
    `;

    return card;
}

// Enhanced modal opening with animation
function openLetterModal(letter) {
    console.log('Opening modal for letter:', letter);

    const modal = document.getElementById('letterModal');
    const modalTitle = document.getElementById('modalTitle');
    const letterViewer = document.getElementById('letterViewer');

    if (!modal || !modalTitle || !letterViewer) {
        console.error('Modal elements not found');
        return;
    }

    modalTitle.textContent = `Letter ${letter.year}/${letter.letterId}`;

    // Get Gregorian year safely
    const gregorianYear = CONFIG && CONFIG.utils ? CONFIG.utils.getGregorianYear(letter.year) : 'Unknown';

    // Create PDF viewer or placeholder
    letterViewer.innerHTML = `
        <div class="letter-details">
            <div class="detail-row">
                <strong>Year:</strong> ${letter.year} (${gregorianYear})
            </div>
            <div class="detail-row">
                <strong>Letter ID:</strong> ${letter.letterId}
            </div>
            <div class="detail-row">
                <strong>Category:</strong> ${letter.category}
            </div>
            <div class="detail-row">
                <strong>File Size:</strong> ${formatFileSize(letter.size)}
            </div>
        </div>
        <div class="letter-content">
            <h4>Letter Preview:</h4>
            <div class="preview-text">${letter.preview}</div>
            <div class="pdf-placeholder">
                <i class="fas fa-file-pdf"></i>
                <p>Click below to open the original PDF letter</p>
                <button class="btn btn-primary" onclick="window.open('${letter.downloadUrl}', '_blank')">
                    <i class="fas fa-external-link-alt"></i>
                    Open PDF Letter
                </button>
                <p style="margin-top: 1rem; font-size: 0.9rem; color: #666;">
                    <strong>Direct URL:</strong><br>
                    <a href="${letter.downloadUrl}" target="_blank" style="word-break: break-all; color: #2563eb;">
                        ${letter.downloadUrl}
                    </a>
                </p>
            </div>
        </div>
    `;

    // Store current letter for download/share functions
    modal.currentLetter = letter;

    // Show modal with proper display and positioning
    modal.style.display = 'flex';
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.zIndex = '9999';
    modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    modal.style.alignItems = 'center';
    modal.style.justifyContent = 'center';

    document.body.style.overflow = 'hidden';

    console.log('Modal should now be visible with proper styling');
}
