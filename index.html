<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>bbe Responsa - Letters & Insights</title>
    <meta name="description" content="Access thousands of letters and insights from the Lubavitcher Rebbe. Digital archive with search and filtering capabilities.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%231B336A'/><text x='50' y='65' font-size='60' text-anchor='middle' fill='white'>ר</text></svg>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Mobile App Header -->
    <header class="app-header">
        <div class="header-content">
            <div class="header-left">
                <div class="app-icon">
                    <svg width="40" height="40" viewBox="0 0 100 100" fill="none">
                        <rect width="100" height="100" rx="12" fill="#1B336A"/>
                        <text x="50" y="65" font-size="60" text-anchor="middle" fill="white" font-family="serif">ר</text>
                    </svg>
                </div>
                <div class="app-title">
                    <h1>Rebbe Responsa</h1>
                    <span class="subtitle">Letters & Insights</span>
                </div>
            </div>
            <div class="header-right">
                <button class="icon-btn" id="searchToggle">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="11.7664" cy="11.7666" r="8.98856" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M18.0181 18.4851L21.5421 22" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                </button>
                <button class="icon-btn" id="filterToggle">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M5.4 2.1H18.6C19.37 2.1 20 2.73 20 3.5V5.4C20 6.17 19.37 6.8 18.6 6.8H5.4C4.63 6.8 4 6.17 4 5.4V3.5C4 2.73 4.63 2.1 5.4 2.1Z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M8.5 6.8V17.5C8.5 18.27 9.13 18.9 9.9 18.9H14.1C14.87 18.9 15.5 18.27 15.5 17.5V6.8" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Search Bar (Hidden by default) -->
        <div class="search-container" id="searchContainer">
            <div class="search-input-wrapper">
                <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <circle cx="11.7664" cy="11.7666" r="8.98856" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M18.0181 18.4851L21.5421 22" stroke="currentColor" stroke-width="1.5"/>
                </svg>
                <input type="text" id="searchInput" placeholder="Search letters, insights, or topics..." class="search-input">
                <button class="clear-search" id="clearSearch">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="tab-navigation">
        <button class="tab-btn active" data-tab="letters">
            <svg width="24" height="24" viewBox="0 0 10 11" fill="none">
                <path d="M8.77007 4.34843L8.74226 6.73638C8.72164 8.79793 8.00957 9.79083 6.31106 10.004C6.03981 10.0425 5.74134 10.0588 5.41562 10.0526L4.47282 10.0379C2.133 10.003 1.20168 9.03984 1.22948 6.65189L1.2561 4.25852C1.26213 3.77284 1.3025 3.34208 1.39024 2.97467C1.72755 1.51954 2.71135 0.926713 4.58511 0.953112L5.52139 0.963596C7.87069 0.990715 8.79787 1.96048 8.77007 4.34843Z" fill="currentColor" stroke="currentColor" stroke-width="0.7"/>
                <path d="M2.83026 5.72336L4.46357 5.77564" stroke="white" stroke-width="0.7"/>
                <path d="M4.25578 8.28054L4.65421 7.84712L4.64821 7.08142L5.07109 8.28741L5.57731 7.8549L5.57131 7.0892L5.88652 8.29428L6.69521 7.44099L7.1495 7.6983" stroke="white" stroke-width="0.3"/>
                <path d="M2.89505 3.50383L5.62572 3.58715" stroke="white" stroke-width="0.7"/>
            </svg>
            <span>Letters</span>
        </button>
        <button class="tab-btn" data-tab="insights">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
            </svg>
            <span>Insights</span>
        </button>
        <button class="tab-btn" data-tab="topics">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M4 6H20M4 12H20M4 18H20" stroke="currentColor" stroke-width="2"/>
            </svg>
            <span>Topics</span>
        </button>
        <button class="tab-btn" data-tab="saved">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
            </svg>
            <span>Saved</span>
        </button>
    </nav>

    <!-- Filter Panel (Hidden by default) -->
    <div class="filter-panel" id="filterPanel">
        <div class="filter-header">
            <h3>Filter & Sort</h3>
            <button class="close-filter" id="closeFilter">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2"/>
                </svg>
            </button>
        </div>
        <div class="filter-content">
            <div class="filter-section">
                <h4>Year Range</h4>
                <div class="year-filters">
                    <button class="filter-chip active" data-year="all">All Years</button>
                    <button class="filter-chip" data-year="5750-5754">5750-5754</button>
                    <button class="filter-chip" data-year="5740-5749">5740-5749</button>
                    <button class="filter-chip" data-year="5730-5739">5730-5739</button>
                    <button class="filter-chip" data-year="5720-5729">5720-5729</button>
                    <button class="filter-chip" data-year="5710-5719">5710-5719</button>
                    <button class="filter-chip" data-year="5700-5709">5700-5709</button>
                </div>
            </div>
            <div class="filter-section">
                <h4>Sort By</h4>
                <div class="sort-options">
                    <label class="radio-option">
                        <input type="radio" name="sort" value="newest" checked>
                        <span>Newest First</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="sort" value="oldest">
                        <span>Oldest First</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="sort" value="relevance">
                        <span>Most Relevant</span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Letters Tab Content -->
        <div class="tab-content active" id="lettersTab">
            <div class="content-header">
                <h2>Recent Letters</h2>
                <span class="item-count" id="letterCount">Loading...</span>
            </div>
            <div class="letters-grid" id="lettersGrid">
                <!-- Letters will be loaded here -->
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>Loading letters...</p>
                </div>
            </div>
            <button class="load-more-btn" id="loadMoreLetters" style="display: none;">
                Load More Letters
            </button>
        </div>

        <!-- Insights Tab Content -->
        <div class="tab-content" id="insightsTab">
            <div class="content-header">
                <h2>Daily Insights</h2>
                <span class="item-count" id="insightCount">Loading...</span>
            </div>
            <div class="insights-list" id="insightsList">
                <!-- Insights will be loaded here -->
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>Loading insights...</p>
                </div>
            </div>
        </div>

        <!-- Topics Tab Content -->
        <div class="tab-content" id="topicsTab">
            <div class="content-header">
                <h2>Browse by Topic</h2>
            </div>
            <div class="topics-grid" id="topicsGrid">
                <!-- Topics will be loaded here -->
            </div>
        </div>

        <!-- Saved Tab Content -->
        <div class="tab-content" id="savedTab">
            <div class="content-header">
                <h2>Saved Items</h2>
                <span class="item-count" id="savedCount">0 items</span>
            </div>
            <div class="saved-list" id="savedList">
                <div class="empty-state">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                        <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <h3>No saved items yet</h3>
                    <p>Tap the bookmark icon on any letter or insight to save it here.</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Letter Detail Modal -->
    <div class="modal-overlay" id="letterModal">
        <div class="modal-content">
            <div class="modal-header">
                <button class="back-btn" id="closeModal">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
                <h3 id="modalTitle">Letter Details</h3>
                <div class="modal-actions">
                    <button class="icon-btn" id="saveLetterBtn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                    <button class="icon-btn" id="shareLetterBtn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M4 12V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V12" stroke="currentColor" stroke-width="2"/>
                            <path d="M16 6L12 2L8 6" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 2V15" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Letter content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
</body>
</html>
