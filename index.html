<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>bbe Responsa - Digital Archive</title>
    <meta name="description" content="Access thousands of letters and insights from the Lubavitcher Rebbe. Complete digital archive with search and filtering.">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%232563eb'/><text x='50' y='65' font-size='60' text-anchor='middle' fill='white'>ר</text></svg>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
        <!-- Header -->
        <header class="main-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">
                        <svg width="48" height="48" viewBox="0 0 100 100" fill="none">
                            <rect width="100" height="100" rx="16" fill="#2563eb"/>
                            <text x="50" y="70" font-size="50" text-anchor="middle" fill="white" font-family="serif">ר</text>
                        </svg>
                    </div>
                    <div class="title-section">
                        <h1>Rebbe Responsa</h1>
                        <p>Digital Archive of the Rebbe's Letters</p>
                    </div>
                </div>

                <div class="header-actions">
                    <div class="search-box">
                        <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        <input type="text" id="globalSearch" placeholder="Search letters, years, topics..." />
                        <button class="clear-btn" id="clearSearch" style="display: none;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>

                    <div class="filter-controls">
                        <select id="yearFilter" class="filter-select">
                            <option value="all">All Years</option>
                            <option value="5750-5754">5750-5754 (1989-1994)</option>
                            <option value="5740-5749">5740-5749 (1979-1989)</option>
                            <option value="5730-5739">5730-5739 (1969-1979)</option>
                            <option value="5720-5729">5720-5729 (1959-1969)</option>
                            <option value="5710-5719">5710-5719 (1949-1959)</option>
                            <option value="5700-5709">5700-5709 (1939-1949)</option>
                        </select>

                        <select id="sortFilter" class="filter-select">
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                            <option value="relevance">Most Relevant</option>
                        </select>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="main-nav">
            <div class="nav-content">
                <button class="nav-btn active" data-section="letters">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                        <path d="M14 2V8H20" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>Letters</span>
                    <span class="count" id="lettersCount">0</span>
                </button>

                <button class="nav-btn" data-section="insights">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                    </svg>
                    <span>Insights</span>
                    <span class="count" id="insightsCount">0</span>
                </button>

                <button class="nav-btn" data-section="topics">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M4 6H20M4 12H20M4 18H20" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>Topics</span>
                </button>

                <button class="nav-btn" data-section="saved">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>Saved</span>
                    <span class="count" id="savedCount">0</span>
                </button>
            </div>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Letters Section -->
            <section class="content-section active" id="letters-section">
                <div class="section-header">
                    <h2>Letters Archive</h2>
                    <div class="section-stats">
                        <span class="stat">
                            <span class="stat-number" id="totalLetters">0</span>
                            <span class="stat-label">Total Letters</span>
                        </span>
                        <span class="stat">
                            <span class="stat-number" id="displayedLetters">0</span>
                            <span class="stat-label">Showing</span>
                        </span>
                    </div>
                </div>

                <div class="letters-container">
                    <div class="letters-grid" id="lettersGrid">
                        <div class="loading-placeholder">
                            <div class="loading-spinner"></div>
                            <p>Loading letters from the archive...</p>
                        </div>
                    </div>

                    <div class="load-more-container">
                        <button class="load-more-btn" id="loadMoreBtn" style="display: none;">
                            <span>Load More Letters</span>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M12 5V19M5 12L12 19L19 12" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Insights Section -->
            <section class="content-section" id="insights-section">
                <div class="section-header">
                    <h2>Daily Insights</h2>
                    <div class="section-stats">
                        <span class="stat">
                            <span class="stat-number" id="totalInsights">0</span>
                            <span class="stat-label">Insights</span>
                        </span>
                    </div>
                </div>

                <div class="insights-container">
                    <div class="insights-grid" id="insightsGrid">
                        <div class="loading-placeholder">
                            <div class="loading-spinner"></div>
                            <p>Loading insights...</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Topics Section -->
            <section class="content-section" id="topics-section">
                <div class="section-header">
                    <h2>Browse by Topic</h2>
                </div>

                <div class="topics-container">
                    <div class="topics-grid" id="topicsGrid">
                        <!-- Topics will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Saved Section -->
            <section class="content-section" id="saved-section">
                <div class="section-header">
                    <h2>Saved Items</h2>
                    <div class="section-stats">
                        <span class="stat">
                            <span class="stat-number" id="totalSaved">0</span>
                            <span class="stat-label">Saved</span>
                        </span>
                    </div>
                </div>

                <div class="saved-container">
                    <div class="saved-grid" id="savedGrid">
                        <div class="empty-state">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                                <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <h3>No saved items yet</h3>
                            <p>Click the bookmark icon on any letter or insight to save it here.</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Letter Detail Modal -->
        <div class="modal-overlay" id="letterModal">
            <div class="modal-container">
                <div class="modal-header">
                    <button class="close-btn" id="closeModal">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                    <h3 id="modalTitle">Letter Details</h3>
                    <div class="modal-actions">
                        <button class="action-btn" id="saveLetterBtn" title="Save Letter">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                        <button class="action-btn" id="shareLetterBtn" title="Share Letter">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M4 12V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V12" stroke="currentColor" stroke-width="2"/>
                                <path d="M16 6L12 2L8 6" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 2V15" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Letter content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
</body>
</html>
