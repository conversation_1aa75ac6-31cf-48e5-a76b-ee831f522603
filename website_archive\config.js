// Firebase Configuration for <PERSON><PERSON> Responsa Website
const CONFIG = {
    // Firebase settings
    firebase: {
        apiKey: 'AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM',
        projectId: 'rebberesponsa',
        storageBucket: 'english-letters.appspot.com',
        baseUrl: 'https://firebasestorage.googleapis.com/v0/b/english-letters.appspot.com/o'
    },
    
    // Application settings
    app: {
        name: 'Rebbe Responsa Digital Archive',
        version: '1.0.0',
        lettersPerPage: 12,
        maxCacheAge: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        enableOfflineMode: true,
        enableAnalytics: false
    },
    
    // UI settings
    ui: {
        animationDuration: 300,
        loadingDelay: 500,
        searchDebounceTime: 300,
        autoSaveSearch: true,
        defaultView: 'grid', // 'grid' or 'list'
        defaultSort: 'year-desc' // 'year-desc', 'year-asc', 'id-asc'
    },
    
    // Letter categories and their display names
    categories: {
        'responsa': 'Responsa',
        'guidance': 'Personal Guidance',
        'blessings': 'Blessings',
        'teachings': 'Teachings',
        'halacha': 'Halachic Rulings',
        'chassidus': 'Chassidic Insights',
        'community': 'Community Affairs',
        'education': 'Education',
        'family': 'Family Matters',
        'business': 'Business Ethics'
    },
    
    // Hebrew years and their Gregorian equivalents
    years: {
        '5702': '1941-1942',
        '5703': '1942-1943',
        '5704': '1943-1944',
        '5705': '1944-1945',
        '5706': '1945-1946',
        '5707': '1946-1947',
        '5708': '1947-1948',
        '5709': '1948-1949',
        '5710': '1949-1950',
        '5711': '1950-1951',
        '5712': '1951-1952',
        '5713': '1952-1953',
        '5714': '1953-1954',
        '5715': '1954-1955',
        '5716': '1955-1956',
        '5717': '1956-1957',
        '5718': '1957-1958',
        '5719': '1958-1959',
        '5720': '1959-1960',
        '5721': '1960-1961',
        '5722': '1961-1962',
        '5723': '1962-1963',
        '5724': '1963-1964',
        '5725': '1964-1965',
        '5726': '1965-1966',
        '5727': '1966-1967',
        '5728': '1967-1968',
        '5729': '1968-1969',
        '5730': '1969-1970',
        '5731': '1970-1971',
        '5732': '1971-1972',
        '5733': '1972-1973',
        '5734': '1973-1974',
        '5735': '1974-1975',
        '5736': '1975-1976',
        '5737': '1976-1977',
        '5738': '1977-1978',
        '5739': '1978-1979',
        '5740': '1979-1980',
        '5741': '1980-1981',
        '5742': '1981-1982',
        '5743': '1982-1983',
        '5744': '1983-1984',
        '5745': '1984-1985',
        '5746': '1985-1986',
        '5747': '1986-1987',
        '5748': '1987-1988',
        '5749': '1988-1989',
        '5750': '1989-1990',
        '5751': '1990-1991',
        '5752': '1991-1992',
        '5753': '1992-1993',
        '5754': '1993-1994'
    },
    
    // Sample letter previews for different types
    letterPreviews: {
        responsa: [
            "ב\"ה\n\nIn response to your halachic question regarding...",
            "בעזהי\"ת\n\nConcerning the matter you raised about...",
            "Your question about the permissibility of..."
        ],
        guidance: [
            "Dear Friend,\n\nI received your letter seeking guidance...",
            "In response to your request for advice...",
            "Regarding your personal situation..."
        ],
        blessings: [
            "May the Almighty bless you with...",
            "With blessings for success in...",
            "Wishing you and your family..."
        ],
        teachings: [
            "The concept you mentioned relates to...",
            "This brings to mind the teaching of...",
            "As explained in Chassidic philosophy..."
        ]
    },
    
    // API endpoints
    api: {
        letters: '/api/letters',
        search: '/api/search',
        download: '/api/download',
        share: '/api/share'
    },
    
    // Error messages
    messages: {
        loading: 'Loading letters from the archive...',
        noResults: 'No letters found matching your search criteria.',
        error: 'An error occurred while loading the letters. Please try again.',
        offline: 'You are currently offline. Showing cached letters.',
        downloadError: 'Unable to download the letter. Please try again.',
        shareError: 'Unable to share the letter. Please try again.'
    },
    
    // Feature flags
    features: {
        enablePDFViewer: true,
        enableFullTextSearch: false, // Requires backend implementation
        enableUserAccounts: false,
        enableFavorites: false,
        enableComments: false,
        enableTranslations: false,
        enableAudioPlayback: false
    }
};

// Utility functions for configuration
CONFIG.utils = {
    // Get Gregorian year from Hebrew year
    getGregorianYear: function(hebrewYear) {
        return this.years[hebrewYear] || 'Unknown';
    },
    
    // Get category display name
    getCategoryName: function(categoryKey) {
        return this.categories[categoryKey] || categoryKey;
    },
    
    // Check if feature is enabled
    isFeatureEnabled: function(featureName) {
        return this.features[featureName] || false;
    },
    
    // Get random letter preview by category
    getRandomPreview: function(category = 'responsa') {
        const previews = this.letterPreviews[category] || this.letterPreviews.responsa;
        return previews[Math.floor(Math.random() * previews.length)];
    },
    
    // Format file size
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // Validate Hebrew year
    isValidHebrewYear: function(year) {
        return /^57\d{2}$/.test(year) && parseInt(year) >= 5702 && parseInt(year) <= 5754;
    },
    
    // Generate letter URL
    generateLetterUrl: function(year, letterId) {
        return `https://RebbeResponsa.app/letters/${year}/${letterId}`;
    }
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else if (typeof window !== 'undefined') {
    window.CONFIG = CONFIG;
}
