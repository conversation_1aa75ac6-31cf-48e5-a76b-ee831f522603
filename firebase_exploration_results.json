{"firestore_collections": {"letters": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "letterTexts": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "content": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "documents": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "responsa": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "igrot": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "igros": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "correspondence": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "archive": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}, "texts": {"found": false, "status": 403, "error": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"Permission denied on resource project rebberesponsa.\",\n    \"status\": \"PERMISSION_DENIED\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com"}}, "realtime_db_paths": {"letters": {"found": false, "status": 404}, "letterTexts": {"found": false, "status": 404}, "content": {"found": false, "status": 404}, "documents": {"found": false, "status": 404}, "responsa": {"found": false, "status": 404}}, "cloud_functions": {"https://us-central1-rebberesponsa.cloudfunctions.net/getLetters": {"found": false, "status": 404}, "https://us-central1-rebberesponsa.cloudfunctions.net/letters": {"found": false, "status": 404}, "https://us-central1-rebberesponsa.cloudfunctions.net/api": {"found": false, "status": 404}, "https://us-central1-rebberesponsa.cloudfunctions.net/listLetters": {"found": false, "status": 404}, "https://us-central1-rebberesponsa.cloudfunctions.net/searchLetters": {"found": false, "status": 404}}, "storage_analysis": {"total_files": 6511, "year_patterns": {"16": 1, "5702": 4, "5703": 33, "5704": 44, "5705": 16, "5706": 2}, "sample_patterns": {"16": ["30955"], "5702": ["24955", "30506", "30544", "30556"], "5703": ["23867", "23867", "24956", "24956", "25292", "26545", "29222", "29222", "29462", "30174", "30508", "30511", "30513", "30514", "30515", "30517", "30518", "30519", "30520", "30521", "30522", "30523", "30526", "30527", "30540", "30542", "30547", "30549", "30551", "30551", "30554", "30558", "30560"], "5704": ["23868", "24921", "24921", "25292", "25294", "25294", "27013", "28004", "30177", "30562", "30564", "30566", "30568", "30576", "30607", "30608", "30611", "30621", "30622", "30624", "30627", "30629", "30631", "30633", "30635", "30637", "30639", "30641", "30786", "30802", "31397", "31809", "31811", "31814", "31816", "31818", "31820", "31822", "31824", "31826", "31828", "31828", "31830", "31845"], "5705": ["20548", "20549", "20550", "20551", "20805", "21553", "21553", "23869", "27465", "27465", "27467", "27467", "29059", "29462", "29462", "31847"], "5706": ["20552", "20553"]}}, "api_endpoints": {}}