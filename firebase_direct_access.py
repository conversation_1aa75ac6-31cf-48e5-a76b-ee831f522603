#!/usr/bin/env python3
"""
Firebase Direct Access Tool
Attempts to access Firebase APIs directly using common patterns
"""

import requests
import json
import sys
from urllib.parse import urljoin

# Common Firebase project patterns for Rebbe/Jewish apps
POTENTIAL_PROJECT_IDS = [
    "rebbe-responsa",
    "rebberesponsa", 
    "rebbe-letters",
    "chabad-responsa",
    "chabad-letters",
    "responsa-app",
    "jewish-responsa",
    "torah-responsa",
    "lubavitch-responsa"
]

# Firebase service endpoints
FIREBASE_ENDPOINTS = {
    "storage": "https://firebasestorage.googleapis.com/v0/b/{project_id}.appspot.com/o",
    "firestore": "https://firestore.googleapis.com/v1/projects/{project_id}/databases/(default)/documents",
    "realtime_db": "https://{project_id}-default-rtdb.firebaseio.com/.json",
    "functions": "https://us-central1-{project_id}.cloudfunctions.net"
}

def test_firebase_endpoint(url, endpoint_type):
    """Test if a Firebase endpoint is accessible"""
    try:
        print(f"Testing {endpoint_type}: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ SUCCESS: {endpoint_type} is accessible!")
            
            # Try to parse JSON response
            try:
                data = response.json()
                print(f"📄 Response preview: {str(data)[:200]}...")
                return True, data
            except:
                print(f"📄 Response (text): {response.text[:200]}...")
                return True, response.text
                
        elif response.status_code == 403:
            print(f"🔒 FORBIDDEN: {endpoint_type} exists but requires authentication")
            return False, "forbidden"
            
        elif response.status_code == 404:
            print(f"❌ NOT FOUND: {endpoint_type} doesn't exist")
            return False, "not_found"
            
        else:
            print(f"⚠️  Status {response.status_code}: {response.text[:100]}")
            return False, f"status_{response.status_code}"
            
    except requests.exceptions.Timeout:
        print(f"⏰ TIMEOUT: {endpoint_type}")
        return False, "timeout"
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: {endpoint_type} - {e}")
        return False, "error"

def test_storage_collections(project_id):
    """Test common storage paths for letters"""
    base_url = f"https://firebasestorage.googleapis.com/v0/b/{project_id}.appspot.com/o"
    
    # Common paths where letters might be stored
    test_paths = [
        "",  # Root
        "letters",
        "responsa", 
        "quotes",
        "insights",
        "data",
        "content",
        "texts"
    ]
    
    results = []
    
    for path in test_paths:
        if path:
            url = f"{base_url}?prefix={path}"
        else:
            url = base_url
            
        success, data = test_firebase_endpoint(url, f"Storage: {path or 'root'}")
        if success:
            results.append((path, data))
            
    return results

def test_firestore_collections(project_id):
    """Test common Firestore collections for letters"""
    base_url = f"https://firestore.googleapis.com/v1/projects/{project_id}/databases/(default)/documents"
    
    # Common collection names
    collections = [
        "letters",
        "responsa",
        "quotes", 
        "insights",
        "content",
        "texts",
        "data"
    ]
    
    results = []
    
    for collection in collections:
        url = f"{base_url}/{collection}"
        success, data = test_firebase_endpoint(url, f"Firestore: {collection}")
        if success:
            results.append((collection, data))
            
    return results

def main():
    print("Firebase Direct Access Tool")
    print("=" * 50)
    print("Attempting to find accessible Firebase endpoints...")
    print()
    
    found_endpoints = []
    
    for project_id in POTENTIAL_PROJECT_IDS:
        print(f"\n🔍 Testing project ID: {project_id}")
        print("-" * 30)
        
        # Test each endpoint type
        for endpoint_type, url_template in FIREBASE_ENDPOINTS.items():
            url = url_template.format(project_id=project_id)
            success, data = test_firebase_endpoint(url, endpoint_type)
            
            if success:
                found_endpoints.append({
                    'project_id': project_id,
                    'endpoint_type': endpoint_type,
                    'url': url,
                    'data': data
                })
        
        # If we found a working project, test specific collections
        if any(e['project_id'] == project_id for e in found_endpoints):
            print(f"\n📂 Testing collections for {project_id}...")
            
            # Test Storage collections
            storage_results = test_storage_collections(project_id)
            for path, data in storage_results:
                found_endpoints.append({
                    'project_id': project_id,
                    'endpoint_type': f'storage_collection_{path}',
                    'url': f"https://firebasestorage.googleapis.com/v0/b/{project_id}.appspot.com/o?prefix={path}",
                    'data': data
                })
            
            # Test Firestore collections  
            firestore_results = test_firestore_collections(project_id)
            for collection, data in firestore_results:
                found_endpoints.append({
                    'project_id': project_id,
                    'endpoint_type': f'firestore_collection_{collection}',
                    'url': f"https://firestore.googleapis.com/v1/projects/{project_id}/databases/(default)/documents/{collection}",
                    'data': data
                })
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    if found_endpoints:
        print(f"✅ Found {len(found_endpoints)} accessible endpoints!")
        
        for endpoint in found_endpoints:
            print(f"\n📍 {endpoint['project_id']} - {endpoint['endpoint_type']}")
            print(f"   URL: {endpoint['url']}")
            
        # Save results
        with open('firebase_accessible_endpoints.json', 'w') as f:
            json.dump(found_endpoints, f, indent=2)
        print(f"\n💾 Results saved to: firebase_accessible_endpoints.json")
        
    else:
        print("❌ No accessible Firebase endpoints found.")
        print("\nThis could mean:")
        print("- The project uses a different naming pattern")
        print("- All endpoints require authentication")
        print("- The data is stored in a different service")

if __name__ == "__main__":
    main()
