#!/usr/bin/env python3
"""
Download Letter Content
Downloads the actual content of specific letters using the discovered file paths
"""

import requests
import json
import time
import os
from datetime import datetime
from urllib.parse import quote

# Firebase configuration
FIREBASE_CONFIG = {
    'apiKey': 'AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM',
    'projectId': 'rebberesponsa',
    'storageBucket': 'english-letters.appspot.com'
}

def download_specific_letter(file_path):
    """Download content of a specific letter file"""
    
    # URL encode the file path
    encoded_path = quote(file_path, safe='')
    
    # Build download URL
    download_url = f"https://firebasestorage.googleapis.com/v0/b/{FIREBASE_CONFIG['storageBucket']}/o/{encoded_path}?alt=media"
    
    print(f"📥 Downloading: {file_path}")
    print(f"    URL: {download_url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
            'Accept': '*/*',
        }
        
        response = requests.get(download_url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            content = response.text
            print(f"    ✅ SUCCESS: {len(content)} bytes")
            print(f"    Preview: {content[:200]}...")
            
            return {
                'file_path': file_path,
                'success': True,
                'content': content,
                'size': len(content)
            }
        else:
            print(f"    ❌ Error: Status {response.status_code}")
            print(f"    Response: {response.text[:100]}")
            
            return {
                'file_path': file_path,
                'success': False,
                'status_code': response.status_code,
                'error': response.text[:200]
            }
            
    except Exception as e:
        print(f"    ❌ Exception: {e}")
        return {
            'file_path': file_path,
            'success': False,
            'error': str(e)
        }

def download_known_letters():
    """Download the specific letters we found"""
    
    # Known letter file paths from the discovery
    known_letters = [
        '5716/20745/ae872089-b599-4c75-b17d-5354ddc7daf3',
        '5716/20745/e15480cd-3272-4b8a-a77a-14291d7da384',
        '5728/21927/b3a44945-2307-4f57-9d1d-ca8e22345547',
        '5744/23455/e58116f0-d38e-46ed-a9bc-2c9c22244643',
    ]
    
    print("📥 Downloading known letter content...")
    print("-" * 60)
    
    results = []
    
    for file_path in known_letters:
        result = download_specific_letter(file_path)
        results.append(result)
        time.sleep(1)  # Be respectful
        print()
    
    return results

def download_sample_letters_by_pattern():
    """Download sample letters using the discovered pattern"""
    
    # Load the complete file list from previous download
    try:
        with open('all_letters_download.json', 'r') as f:
            data = json.load(f)
            all_items = data.get('all_items', [])
    except:
        print("❌ Could not load previous file list")
        return []
    
    # Find files that look like letters (year/id/uuid pattern)
    letter_pattern_files = []
    
    for item in all_items:
        name = item.get('name', '')
        
        # Look for pattern: year/id/uuid (e.g., 5716/20745/uuid)
        parts = name.split('/')
        if len(parts) == 3:
            year, letter_id, uuid = parts
            
            # Check if year looks like Hebrew year (57xx)
            if year.startswith('57') and len(year) == 4 and year.isdigit():
                # Check if letter_id is numeric
                if letter_id.isdigit():
                    letter_pattern_files.append({
                        'name': name,
                        'year': year,
                        'letter_id': letter_id,
                        'uuid': uuid,
                        'size': item.get('size', 0)
                    })
    
    print(f"📊 Found {len(letter_pattern_files)} files matching letter pattern")
    
    # Download a sample from different years
    sample_files = []
    years_sampled = set()
    
    for file_info in letter_pattern_files:
        if len(sample_files) >= 10:  # Limit sample size
            break
            
        year = file_info['year']
        if year not in years_sampled or len(years_sampled) < 5:
            sample_files.append(file_info)
            years_sampled.add(year)
    
    print(f"📥 Downloading {len(sample_files)} sample letters...")
    print("-" * 60)
    
    results = []
    
    for file_info in sample_files:
        result = download_specific_letter(file_info['name'])
        if result['success']:
            result.update(file_info)  # Add year, letter_id, etc.
        results.append(result)
        time.sleep(1)
        print()
    
    return results

def analyze_letter_content(results):
    """Analyze the downloaded letter content"""
    
    print("🔍 Analyzing letter content...")
    print("-" * 40)
    
    successful_downloads = [r for r in results if r['success']]
    
    if not successful_downloads:
        print("❌ No successful downloads to analyze")
        return
    
    for result in successful_downloads:
        content = result['content']
        file_path = result['file_path']
        
        print(f"\n📄 Letter: {file_path}")
        print(f"   Size: {result['size']} bytes")
        
        # Try to parse as JSON
        try:
            data = json.loads(content)
            print(f"   ✅ Valid JSON")
            print(f"   Keys: {list(data.keys()) if isinstance(data, dict) else type(data)}")
            
            # Look for letter text
            if isinstance(data, dict):
                for key in ['text', 'content', 'body', 'letter', 'hebrew', 'english']:
                    if key in data:
                        text_content = data[key]
                        if isinstance(text_content, str) and len(text_content) > 50:
                            print(f"   📝 Found text in '{key}': {text_content[:100]}...")
                            
        except json.JSONDecodeError:
            print(f"   📄 Plain text content")
            print(f"   Preview: {content[:200]}...")
        
        # Look for Hebrew/English content
        if any(char in content for char in 'אבגדהוזחטיכלמנסעפצקרשת'):
            print(f"   🔤 Contains Hebrew text")
        
        if any(word in content.lower() for word in ['rebbe', 'lubavitch', 'chabad', 'letter', 'dear']):
            print(f"   📮 Contains letter-related keywords")

def main():
    print("Download Letter Content")
    print("=" * 60)
    print("Downloading actual letter content using discovered file paths...")
    print()
    
    # Download known letters first
    known_results = download_known_letters()
    
    # Download sample letters using pattern
    sample_results = download_sample_letters_by_pattern()
    
    # Combine results
    all_results = known_results + sample_results
    
    # Analyze content
    analyze_letter_content(all_results)
    
    # Summary
    print("\n" + "=" * 60)
    print("DOWNLOAD RESULTS")
    print("=" * 60)
    
    successful = [r for r in all_results if r['success']]
    failed = [r for r in all_results if not r['success']]
    
    print(f"✅ Successful downloads: {len(successful)}")
    print(f"❌ Failed downloads: {len(failed)}")
    
    if successful:
        print(f"\n📄 Successfully downloaded letters:")
        for result in successful:
            file_path = result['file_path']
            size = result['size']
            
            # Extract year/letter_id if available
            parts = file_path.split('/')
            if len(parts) >= 2:
                year, letter_id = parts[0], parts[1]
                print(f"   📝 Year {year}, Letter {letter_id}: {size} bytes")
            else:
                print(f"   📝 {file_path}: {size} bytes")
    
    if failed:
        print(f"\n❌ Failed downloads:")
        for result in failed:
            print(f"   {result['file_path']}: {result.get('error', 'Unknown error')}")
    
    # Save results
    output = {
        'timestamp': datetime.now().isoformat(),
        'known_letters': known_results,
        'sample_letters': sample_results,
        'summary': {
            'total_attempts': len(all_results),
            'successful': len(successful),
            'failed': len(failed)
        }
    }
    
    with open('letter_content_download.json', 'w') as f:
        json.dump(output, f, indent=2)
    
    print(f"\n💾 Results saved to: letter_content_download.json")
    
    if successful:
        print(f"\n🎯 SUCCESS! We can now access the actual letter content!")
        print("Next steps:")
        print("1. Download all 6,000+ letters systematically")
        print("2. Parse and extract the letter text")
        print("3. Organize by year, topic, and keywords")
        print("4. Create a searchable database")

if __name__ == "__main__":
    main()
