#!/usr/bin/env python3
"""
Firebase Reverse Engineering Tool for <PERSON><PERSON> Responsa
Attempts to discover the actual data structure used by the original app
"""

import requests
import json
import time
from urllib.parse import quote

class FirebaseReverseEngineer:
    def __init__(self):
        self.project_id = "rebberesponsa"
        self.api_key = "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"
        self.storage_bucket = "english-letters.appspot.com"
        
        # Known patterns from the original app
        self.known_years = ['5711', '5716', '5743', '5750', '5752']
        self.known_ids = ['20586', '29797', '23455', '23867']
        
        self.results = {
            'firestore_collections': {},
            'realtime_db_paths': {},
            'cloud_functions': {},
            'storage_analysis': {},
            'api_endpoints': {}
        }
    
    def explore_firestore_rest_api(self):
        """Explore Firestore using REST API"""
        print("🔍 Exploring Firestore via REST API...")
        
        base_url = f"https://firestore.googleapis.com/v1/projects/{self.project_id}/databases/(default)/documents"
        
        # Try to list root collections
        try:
            response = requests.get(f"{base_url}?key={self.api_key}")
            print(f"📊 Firestore root response: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"📄 Root data: {json.dumps(data, indent=2)[:500]}...")
                self.results['firestore_collections']['root'] = data
        except Exception as e:
            print(f"❌ Firestore root error: {e}")
        
        # Try common collection names
        collections = [
            'letters', 'letterTexts', 'content', 'documents', 'responsa',
            'igrot', 'igros', 'correspondence', 'archive', 'texts'
        ]
        
        for collection in collections:
            try:
                url = f"{base_url}/{collection}?key={self.api_key}"
                response = requests.get(url)
                
                print(f"🔍 Collection '{collection}': {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    if 'documents' in data and len(data['documents']) > 0:
                        print(f"✅ FOUND collection '{collection}' with {len(data['documents'])} documents")
                        print(f"📄 Sample document: {json.dumps(data['documents'][0], indent=2)[:300]}...")
                        self.results['firestore_collections'][collection] = {
                            'found': True,
                            'count': len(data['documents']),
                            'sample': data['documents'][0]
                        }
                    else:
                        self.results['firestore_collections'][collection] = {'found': False, 'empty': True}
                else:
                    self.results['firestore_collections'][collection] = {
                        'found': False, 
                        'status': response.status_code,
                        'error': response.text[:200]
                    }
                    
            except Exception as e:
                print(f"❌ Collection '{collection}' error: {e}")
                self.results['firestore_collections'][collection] = {'found': False, 'error': str(e)}
    
    def explore_realtime_database(self):
        """Explore Realtime Database"""
        print("🔍 Exploring Realtime Database...")
        
        base_url = f"https://{self.project_id}-default-rtdb.firebaseio.com"
        
        # Try root
        try:
            response = requests.get(f"{base_url}/.json?auth={self.api_key}")
            print(f"📊 Realtime DB root response: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data:
                    print(f"✅ FOUND Realtime DB root data!")
                    print(f"📄 Root keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                    print(f"📄 Sample data: {json.dumps(data, indent=2)[:500]}...")
                    self.results['realtime_db_paths']['root'] = {
                        'found': True,
                        'keys': list(data.keys()) if isinstance(data, dict) else [],
                        'sample': data
                    }
                else:
                    print("❌ Realtime DB root is empty")
                    self.results['realtime_db_paths']['root'] = {'found': False, 'empty': True}
        except Exception as e:
            print(f"❌ Realtime DB root error: {e}")
            self.results['realtime_db_paths']['root'] = {'found': False, 'error': str(e)}
        
        # Try common paths
        paths = ['letters', 'letterTexts', 'content', 'documents', 'responsa']
        
        for path in paths:
            try:
                response = requests.get(f"{base_url}/{path}.json?auth={self.api_key}")
                print(f"🔍 Path '{path}': {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data:
                        print(f"✅ FOUND path '{path}'!")
                        if isinstance(data, dict):
                            print(f"📄 Keys: {list(data.keys())[:10]}...")
                            print(f"📄 Sample: {json.dumps(list(data.values())[0] if data else {}, indent=2)[:300]}...")
                        self.results['realtime_db_paths'][path] = {
                            'found': True,
                            'type': type(data).__name__,
                            'sample': data
                        }
                    else:
                        self.results['realtime_db_paths'][path] = {'found': False, 'empty': True}
                else:
                    self.results['realtime_db_paths'][path] = {
                        'found': False,
                        'status': response.status_code
                    }
            except Exception as e:
                print(f"❌ Path '{path}' error: {e}")
                self.results['realtime_db_paths'][path] = {'found': False, 'error': str(e)}
    
    def explore_cloud_functions(self):
        """Try to discover Cloud Functions"""
        print("🔍 Exploring Cloud Functions...")
        
        function_patterns = [
            f"https://us-central1-{self.project_id}.cloudfunctions.net/getLetters",
            f"https://us-central1-{self.project_id}.cloudfunctions.net/letters",
            f"https://us-central1-{self.project_id}.cloudfunctions.net/api",
            f"https://us-central1-{self.project_id}.cloudfunctions.net/listLetters",
            f"https://us-central1-{self.project_id}.cloudfunctions.net/searchLetters"
        ]
        
        for url in function_patterns:
            try:
                response = requests.get(url, timeout=10)
                print(f"🔍 Function '{url}': {response.status_code}")
                
                if response.status_code == 200:
                    print(f"✅ FOUND working function: {url}")
                    print(f"📄 Response: {response.text[:300]}...")
                    self.results['cloud_functions'][url] = {
                        'found': True,
                        'status': response.status_code,
                        'response': response.text[:1000]
                    }
                else:
                    self.results['cloud_functions'][url] = {
                        'found': False,
                        'status': response.status_code
                    }
            except Exception as e:
                print(f"❌ Function '{url}' error: {e}")
                self.results['cloud_functions'][url] = {'found': False, 'error': str(e)}
    
    def analyze_storage_patterns(self):
        """Analyze Firebase Storage patterns"""
        print("🔍 Analyzing Firebase Storage patterns...")
        
        # Load the known storage data
        try:
            with open('all_letters_download.json', 'r') as f:
                storage_data = json.load(f)
            
            print(f"📊 Storage analysis: {storage_data['total_files']} files")
            
            # Analyze patterns
            patterns = {}
            for item in storage_data['all_items'][:100]:  # Sample first 100
                path_parts = item['name'].split('/')
                if len(path_parts) >= 2:
                    year = path_parts[0]
                    letter_id = path_parts[1]
                    
                    if year not in patterns:
                        patterns[year] = []
                    patterns[year].append(letter_id)
            
            print(f"📊 Year patterns found: {list(patterns.keys())}")
            for year, ids in patterns.items():
                print(f"  {year}: {len(ids)} letters, sample IDs: {ids[:5]}")
            
            self.results['storage_analysis'] = {
                'total_files': storage_data['total_files'],
                'year_patterns': {year: len(ids) for year, ids in patterns.items()},
                'sample_patterns': patterns
            }
            
        except Exception as e:
            print(f"❌ Storage analysis error: {e}")
            self.results['storage_analysis'] = {'error': str(e)}
    
    def try_known_letter_access(self):
        """Try to access specific known letters"""
        print("🔍 Trying to access known letters...")
        
        # Try different document ID patterns for known letters
        for year in self.known_years:
            for letter_id in self.known_ids:
                patterns = [
                    f"{year}-{letter_id}",
                    f"{letter_id}",
                    f"letter_{letter_id}",
                    f"{year}_{letter_id}",
                    f"L{letter_id}",
                    f"letter-{year}-{letter_id}"
                ]
                
                for pattern in patterns:
                    # Try Firestore
                    try:
                        url = f"https://firestore.googleapis.com/v1/projects/{self.project_id}/databases/(default)/documents/letters/{pattern}?key={self.api_key}"
                        response = requests.get(url)
                        
                        if response.status_code == 200:
                            data = response.json()
                            print(f"✅ FOUND LETTER in Firestore! Pattern: {pattern}")
                            print(f"📄 Data: {json.dumps(data, indent=2)[:500]}...")
                            return {'type': 'firestore', 'pattern': pattern, 'data': data}
                    except:
                        pass
                    
                    # Try Realtime DB
                    try:
                        url = f"https://{self.project_id}-default-rtdb.firebaseio.com/letters/{pattern}.json?auth={self.api_key}"
                        response = requests.get(url)
                        
                        if response.status_code == 200:
                            data = response.json()
                            if data:
                                print(f"✅ FOUND LETTER in Realtime DB! Pattern: {pattern}")
                                print(f"📄 Data: {json.dumps(data, indent=2)[:500]}...")
                                return {'type': 'realtime', 'pattern': pattern, 'data': data}
                    except:
                        pass
        
        print("❌ No known letters found with standard patterns")
        return None
    
    def run_full_exploration(self):
        """Run complete reverse engineering exploration"""
        print("🚀 Starting comprehensive Firebase reverse engineering...")
        print("=" * 60)
        
        self.explore_firestore_rest_api()
        print()
        
        self.explore_realtime_database()
        print()
        
        self.explore_cloud_functions()
        print()
        
        self.analyze_storage_patterns()
        print()
        
        letter_result = self.try_known_letter_access()
        if letter_result:
            self.results['found_letter'] = letter_result
        
        print("=" * 60)
        print("📊 EXPLORATION SUMMARY:")
        print("=" * 60)
        
        # Summarize findings
        firestore_found = [k for k, v in self.results['firestore_collections'].items() if v.get('found')]
        realtime_found = [k for k, v in self.results['realtime_db_paths'].items() if v.get('found')]
        functions_found = [k for k, v in self.results['cloud_functions'].items() if v.get('found')]
        
        if firestore_found:
            print(f"✅ Firestore collections found: {firestore_found}")
        else:
            print("❌ No Firestore collections accessible")
        
        if realtime_found:
            print(f"✅ Realtime DB paths found: {realtime_found}")
        else:
            print("❌ No Realtime DB paths accessible")
        
        if functions_found:
            print(f"✅ Cloud Functions found: {functions_found}")
        else:
            print("❌ No Cloud Functions accessible")
        
        if 'found_letter' in self.results:
            print(f"✅ Found actual letter data: {self.results['found_letter']['type']} - {self.results['found_letter']['pattern']}")
        else:
            print("❌ No actual letter data found")
        
        # Save results
        with open('firebase_exploration_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Results saved to firebase_exploration_results.json")
        return self.results

if __name__ == "__main__":
    explorer = FirebaseReverseEngineer()
    results = explorer.run_full_exploration()
