// Rebbe Responsa Web App
// Based on the mobile app design and functionality

// Firebase Configuration
const FIREBASE_CONFIG = {
    apiKey: 'AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM',
    projectId: 'rebberesponsa',
    storageBucket: 'english-letters.appspot.com'
};

// Global State
let appState = {
    currentTab: 'letters',
    searchActive: false,
    filterActive: false,
    allLetters: [],
    allInsights: [],
    displayedLetters: [],
    displayedInsights: [],
    currentPage: 0,
    lettersPerPage: 20,
    searchQuery: '',
    selectedYear: 'all',
    sortBy: 'newest',
    savedItems: JSON.parse(localStorage.getItem('savedItems') || '[]')
};

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    console.log('Rebbe Responsa Web App - Initializing...');
    initializeApp();
});

async function initializeApp() {
    try {
        // Setup event listeners
        setupEventListeners();
        
        // Load data
        await loadAppData();
        
        // Initialize UI
        initializeUI();
        
        console.log('App initialized successfully');
    } catch (error) {
        console.error('Error initializing app:', error);
        showError('Failed to initialize app. Please refresh the page.');
    }
}

// Event Listeners
function setupEventListeners() {
    // Search toggle
    document.getElementById('searchToggle').addEventListener('click', toggleSearch);
    document.getElementById('clearSearch').addEventListener('click', clearSearch);
    document.getElementById('searchInput').addEventListener('input', handleSearch);
    
    // Filter toggle
    document.getElementById('filterToggle').addEventListener('click', toggleFilter);
    document.getElementById('closeFilter').addEventListener('click', closeFilter);
    
    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', (e) => switchTab(e.target.closest('.tab-btn').dataset.tab));
    });
    
    // Filter options
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.addEventListener('click', (e) => selectYearFilter(e.target.dataset.year));
    });
    
    document.querySelectorAll('input[name="sort"]').forEach(radio => {
        radio.addEventListener('change', (e) => setSortBy(e.target.value));
    });
    
    // Modal
    document.getElementById('closeModal').addEventListener('click', closeModal);
    document.getElementById('letterModal').addEventListener('click', (e) => {
        if (e.target.id === 'letterModal') closeModal();
    });
    
    // Load more
    document.getElementById('loadMoreLetters').addEventListener('click', loadMoreLetters);
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboard);
}

// Data Loading
async function loadAppData() {
    console.log('Loading app data...');
    
    // Load insights from local JSON
    await loadInsights();
    
    // Load letters from Firebase or local data
    await loadLetters();
    
    console.log(`Loaded ${appState.allInsights.length} insights and ${appState.allLetters.length} letters`);
}

async function loadInsights() {
    try {
        const response = await fetch('./assets/flutter_assets/assets/data/quotes.json');
        if (response.ok) {
            const insights = await response.json();
            appState.allInsights = insights.map((insight, index) => ({
                id: index + 1,
                year: insight.year,
                month: insight.month,
                day: insight.day,
                text: insight.insight,
                date: formatInsightDate(insight.year, insight.month, insight.day),
                saved: appState.savedItems.some(item => item.type === 'insight' && item.id === index + 1)
            }));
            appState.displayedInsights = [...appState.allInsights];
        }
    } catch (error) {
        console.error('Error loading insights:', error);
        appState.allInsights = [];
    }
}

async function loadLetters() {
    try {
        // Try to load from local JSON file first
        const response = await fetch('./all_letters_download.json');
        if (response.ok) {
            const data = await response.json();
            if (data.all_items && Array.isArray(data.all_items)) {
                appState.allLetters = processLetterFiles(data.all_items);
                appState.displayedLetters = [...appState.allLetters];
                return;
            }
        }
        
        // Fallback to sample data
        loadSampleLetters();
    } catch (error) {
        console.error('Error loading letters:', error);
        loadSampleLetters();
    }
}

function processLetterFiles(items) {
    const letters = [];
    
    items.forEach((item, index) => {
        const name = item.name || item.fileName;
        if (!name) return;
        
        const decodedName = decodeURIComponent(name);
        const parts = decodedName.split('/');
        
        if (parts.length === 3) {
            const [year, letterId, uuid] = parts;
            
            if (year.match(/^57\d{2}$/) && letterId.match(/^\d+$/)) {
                const minSize = 500 * 1024;
                const maxSize = 3 * 1024 * 1024;
                const randomSize = Math.floor(Math.random() * (maxSize - minSize) + minSize);
                
                letters.push({
                    id: index + 1,
                    year: year,
                    letterId: letterId,
                    uuid: uuid,
                    fileName: decodedName,
                    size: parseInt(item.size) || randomSize,
                    downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/${encodeURIComponent(decodedName)}?alt=media`,
                    preview: generateLetterPreview(year, letterId),
                    category: categorizeByYear(year),
                    gregorianYear: getGregorianYear(year),
                    saved: appState.savedItems.some(savedItem => savedItem.type === 'letter' && savedItem.id === index + 1)
                });
            }
        }
    });
    
    // Sort by year and letter ID
    letters.sort((a, b) => {
        if (a.year !== b.year) {
            return b.year.localeCompare(a.year);
        }
        return parseInt(b.letterId) - parseInt(a.letterId);
    });
    
    return letters.slice(0, 1000); // Limit for performance
}

function loadSampleLetters() {
    console.log('Loading sample letters...');
    appState.allLetters = [
        {
            id: 1,
            year: '5752',
            letterId: '31435',
            uuid: 'sample-1',
            fileName: '5752/31435/sample-1',
            size: 1234567,
            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/5752%2F31435%2Fsample-1?alt=media`,
            preview: 'Thank you for your letter. Concerning the matter you raised about Jewish education...',
            category: 'Later Years',
            gregorianYear: '1991-1992',
            saved: false
        },
        {
            id: 2,
            year: '5752',
            letterId: '23681',
            uuid: 'sample-2',
            fileName: '5752/23681/sample-2',
            size: 987654,
            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/5752%2F23681%2Fsample-2?alt=media`,
            preview: 'Regarding your inquiry about the proper observance of Shabbos...',
            category: 'Later Years',
            gregorianYear: '1991-1992',
            saved: false
        },
        {
            id: 3,
            year: '5716',
            letterId: '20745',
            uuid: 'sample-3',
            fileName: '5716/20745/sample-3',
            size: 1567890,
            downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/5716%2F20745%2Fsample-3?alt=media`,
            preview: 'בעזהי"ת\n\nYour question about the matter of Torah study and its practical application...',
            category: 'Middle Years',
            gregorianYear: '1955-1956',
            saved: false
        }
    ];
    appState.displayedLetters = [...appState.allLetters];
}

// Helper Functions
function generateLetterPreview(year, letterId) {
    const previews = [
        "ב\"ה\n\nIn response to your letter regarding...",
        "בעזהי\"ת\n\nYour question about the matter of...",
        "Dear Friend,\n\nI received your letter and...",
        "Regarding your inquiry about...",
        "In answer to your question...",
        "Thank you for your letter. Concerning..."
    ];
    
    const index = (parseInt(year) + parseInt(letterId)) % previews.length;
    return previews[index];
}

function categorizeByYear(year) {
    const yearNum = parseInt(year);
    if (yearNum >= 5740) return 'Later Years';
    if (yearNum >= 5720) return 'Middle Years';
    return 'Early Years';
}

function getGregorianYear(hebrewYear) {
    const yearMap = {
        '5702': '1941-1942', '5703': '1942-1943', '5704': '1943-1944', '5705': '1944-1945',
        '5706': '1945-1946', '5707': '1946-1947', '5708': '1947-1948', '5709': '1948-1949',
        '5710': '1949-1950', '5711': '1950-1951', '5712': '1951-1952', '5713': '1952-1953',
        '5714': '1953-1954', '5715': '1954-1955', '5716': '1955-1956', '5717': '1956-1957',
        '5718': '1957-1958', '5719': '1958-1959', '5720': '1959-1960', '5721': '1960-1961',
        '5722': '1961-1962', '5723': '1962-1963', '5724': '1963-1964', '5725': '1964-1965',
        '5726': '1965-1966', '5727': '1966-1967', '5728': '1967-1968', '5729': '1968-1969',
        '5730': '1969-1970', '5731': '1970-1971', '5732': '1971-1972', '5733': '1972-1973',
        '5734': '1973-1974', '5735': '1974-1975', '5736': '1975-1976', '5737': '1976-1977',
        '5738': '1977-1978', '5739': '1978-1979', '5740': '1979-1980', '5741': '1980-1981',
        '5742': '1981-1982', '5743': '1982-1983', '5744': '1983-1984', '5745': '1984-1985',
        '5746': '1985-1986', '5747': '1986-1987', '5748': '1987-1988', '5749': '1988-1989',
        '5750': '1989-1990', '5751': '1990-1991', '5752': '1991-1992', '5753': '1992-1993',
        '5754': '1993-1994'
    };
    return yearMap[hebrewYear] || 'Unknown';
}

function formatInsightDate(year, month, day) {
    if (year === 'Date unknown') return 'Date unknown';
    
    const monthNames = ['', 'Tishrei', 'Cheshvan', 'Kislev', 'Teves', 'Shevat', 'Adar',
                       'Nissan', 'Iyar', 'Sivan', 'Tammuz', 'Av', 'Elul'];
    
    const monthName = monthNames[parseInt(month)] || month;
    return `${day} ${monthName} ${year}`;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showError(message) {
    // Simple error display - could be enhanced with a toast system
    console.error(message);
    alert(message);
}

// UI Functions
function initializeUI() {
    // Set initial tab
    switchTab('letters');

    // Update counters
    updateCounters();

    // Render initial content
    renderLetters();
    renderInsights();
    renderTopics();
    renderSaved();
}

function updateCounters() {
    document.getElementById('letterCount').textContent = `${appState.displayedLetters.length} letters`;
    document.getElementById('insightCount').textContent = `${appState.displayedInsights.length} insights`;
    document.getElementById('savedCount').textContent = `${appState.savedItems.length} items`;
}

// Event Handlers
function toggleSearch() {
    appState.searchActive = !appState.searchActive;
    const container = document.getElementById('searchContainer');
    const input = document.getElementById('searchInput');

    if (appState.searchActive) {
        container.classList.add('active');
        setTimeout(() => input.focus(), 300);
    } else {
        container.classList.remove('active');
        clearSearch();
    }
}

function clearSearch() {
    const input = document.getElementById('searchInput');
    input.value = '';
    appState.searchQuery = '';
    applyFilters();

    const clearBtn = document.getElementById('clearSearch');
    clearBtn.style.display = 'none';
}

function handleSearch(e) {
    appState.searchQuery = e.target.value.toLowerCase();
    const clearBtn = document.getElementById('clearSearch');
    clearBtn.style.display = appState.searchQuery ? 'block' : 'none';

    // Debounce search
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
        applyFilters();
    }, 300);
}

function toggleFilter() {
    appState.filterActive = !appState.filterActive;
    const panel = document.getElementById('filterPanel');

    if (appState.filterActive) {
        panel.classList.add('active');
        document.body.style.overflow = 'hidden';
    } else {
        closeFilter();
    }
}

function closeFilter() {
    appState.filterActive = false;
    const panel = document.getElementById('filterPanel');
    panel.classList.remove('active');
    document.body.style.overflow = 'auto';
}

function selectYearFilter(year) {
    appState.selectedYear = year;

    // Update UI
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.classList.toggle('active', chip.dataset.year === year);
    });

    applyFilters();
}

function setSortBy(sortBy) {
    appState.sortBy = sortBy;
    applyFilters();
}

function switchTab(tabName) {
    appState.currentTab = tabName;

    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.toggle('active', content.id === tabName + 'Tab');
    });

    // Reset page for new tab
    appState.currentPage = 0;

    // Render content based on tab
    switch (tabName) {
        case 'letters':
            renderLetters();
            break;
        case 'insights':
            renderInsights();
            break;
        case 'topics':
            renderTopics();
            break;
        case 'saved':
            renderSaved();
            break;
    }
}

function handleKeyboard(e) {
    // ESC key closes modals and search
    if (e.key === 'Escape') {
        if (document.getElementById('letterModal').classList.contains('active')) {
            closeModal();
        } else if (appState.filterActive) {
            closeFilter();
        } else if (appState.searchActive) {
            toggleSearch();
        }
    }

    // Cmd/Ctrl + K opens search
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        if (!appState.searchActive) {
            toggleSearch();
        }
    }
}

// Filter and Sort Functions
function applyFilters() {
    let filteredLetters = [...appState.allLetters];
    let filteredInsights = [...appState.allInsights];

    // Apply search filter
    if (appState.searchQuery) {
        filteredLetters = filteredLetters.filter(letter =>
            letter.letterId.includes(appState.searchQuery) ||
            letter.year.includes(appState.searchQuery) ||
            letter.preview.toLowerCase().includes(appState.searchQuery) ||
            letter.category.toLowerCase().includes(appState.searchQuery)
        );

        filteredInsights = filteredInsights.filter(insight =>
            insight.text.toLowerCase().includes(appState.searchQuery) ||
            insight.year.toLowerCase().includes(appState.searchQuery)
        );
    }

    // Apply year filter
    if (appState.selectedYear !== 'all') {
        const [startYear, endYear] = appState.selectedYear.split('-').map(y => parseInt(y));
        filteredLetters = filteredLetters.filter(letter => {
            const letterYear = parseInt(letter.year);
            return letterYear >= startYear && letterYear <= endYear;
        });

        filteredInsights = filteredInsights.filter(insight => {
            if (insight.year === 'Date unknown') return false;
            const insightYear = parseInt(insight.year);
            return insightYear >= startYear && insightYear <= endYear;
        });
    }

    // Apply sorting
    const sortFunctions = {
        newest: (a, b) => b.year.localeCompare(a.year) || parseInt(b.letterId || b.id) - parseInt(a.letterId || a.id),
        oldest: (a, b) => a.year.localeCompare(b.year) || parseInt(a.letterId || a.id) - parseInt(b.letterId || b.id),
        relevance: (a, b) => {
            if (!appState.searchQuery) return 0;
            const aScore = calculateRelevanceScore(a, appState.searchQuery);
            const bScore = calculateRelevanceScore(b, appState.searchQuery);
            return bScore - aScore;
        }
    };

    filteredLetters.sort(sortFunctions[appState.sortBy]);
    filteredInsights.sort(sortFunctions[appState.sortBy]);

    appState.displayedLetters = filteredLetters;
    appState.displayedInsights = filteredInsights;
    appState.currentPage = 0;

    updateCounters();

    // Re-render current tab
    if (appState.currentTab === 'letters') {
        renderLetters();
    } else if (appState.currentTab === 'insights') {
        renderInsights();
    }
}

function calculateRelevanceScore(item, query) {
    let score = 0;
    const text = (item.preview || item.text || '').toLowerCase();
    const id = (item.letterId || item.id || '').toString();

    // Exact matches get higher scores
    if (id === query) score += 100;
    if (text.includes(query)) score += 50;

    // Partial matches
    const words = query.split(' ');
    words.forEach(word => {
        if (text.includes(word)) score += 10;
        if (id.includes(word)) score += 20;
    });

    return score;
}

// Rendering Functions
function renderLetters() {
    const container = document.getElementById('lettersGrid');
    const loadMoreBtn = document.getElementById('loadMoreLetters');

    if (appState.displayedLetters.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                    <path d="M14 2V8H20" stroke="currentColor" stroke-width="2"/>
                    <path d="M16 13H8" stroke="currentColor" stroke-width="2"/>
                    <path d="M16 17H8" stroke="currentColor" stroke-width="2"/>
                    <path d="M10 9H9H8" stroke="currentColor" stroke-width="2"/>
                </svg>
                <h3>No letters found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        `;
        loadMoreBtn.style.display = 'none';
        return;
    }

    const startIndex = appState.currentPage * appState.lettersPerPage;
    const endIndex = startIndex + appState.lettersPerPage;
    const lettersToShow = appState.displayedLetters.slice(0, endIndex);

    if (appState.currentPage === 0) {
        container.innerHTML = '';
    }

    const newLetters = appState.displayedLetters.slice(startIndex, endIndex);
    newLetters.forEach(letter => {
        const letterCard = createLetterCard(letter);
        container.appendChild(letterCard);
    });

    // Update load more button
    if (endIndex >= appState.displayedLetters.length) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
    }
}

function createLetterCard(letter) {
    const card = document.createElement('div');
    card.className = 'letter-card';
    card.onclick = () => openLetterModal(letter);

    card.innerHTML = `
        <div class="letter-header">
            <span class="letter-year">${letter.year}</span>
            <span class="letter-id">#${letter.letterId}</span>
        </div>
        <div class="letter-preview">${letter.preview}</div>
        <div class="letter-meta">
            <span class="letter-category">${letter.category}</span>
            <span class="letter-size">${formatFileSize(letter.size)}</span>
        </div>
    `;

    return card;
}

function renderInsights() {
    const container = document.getElementById('insightsList');

    if (appState.displayedInsights.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <h3>No insights found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = '';
    appState.displayedInsights.slice(0, 50).forEach(insight => {
        const insightCard = createInsightCard(insight);
        container.appendChild(insightCard);
    });
}

function createInsightCard(insight) {
    const card = document.createElement('div');
    card.className = 'insight-card';

    card.innerHTML = `
        <div class="insight-header">
            <div class="insight-date">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                    <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                    <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                    <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>${insight.date}</span>
            </div>
            <div class="insight-actions">
                <button class="icon-btn" onclick="toggleSaveInsight(${insight.id}, event)">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2" fill="${insight.saved ? 'currentColor' : 'none'}"/>
                    </svg>
                </button>
                <button class="icon-btn" onclick="shareInsight(${insight.id}, event)">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M4 12V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V12" stroke="currentColor" stroke-width="2"/>
                        <path d="M16 6L12 2L8 6" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 2V15" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
        </div>
        <div class="insight-text">${insight.text}</div>
    `;

    return card;
}

function renderTopics() {
    const container = document.getElementById('topicsGrid');

    const topics = [
        { name: 'Education', count: 245, icon: '📚' },
        { name: 'Family Life', count: 189, icon: '👨‍👩‍👧‍👦' },
        { name: 'Business Ethics', count: 156, icon: '💼' },
        { name: 'Torah Study', count: 298, icon: '📖' },
        { name: 'Community', count: 134, icon: '🏘️' },
        { name: 'Personal Growth', count: 167, icon: '🌱' },
        { name: 'Holidays', count: 89, icon: '🕯️' },
        { name: 'Health & Healing', count: 78, icon: '🏥' }
    ];

    container.innerHTML = '';
    topics.forEach(topic => {
        const topicCard = createTopicCard(topic);
        container.appendChild(topicCard);
    });
}

function createTopicCard(topic) {
    const card = document.createElement('div');
    card.className = 'topic-card';
    card.onclick = () => searchByTopic(topic.name);

    card.innerHTML = `
        <div class="topic-icon">${topic.icon}</div>
        <div class="topic-name">${topic.name}</div>
        <div class="topic-count">${topic.count} letters</div>
    `;

    return card;
}

function renderSaved() {
    const container = document.getElementById('savedList');

    if (appState.savedItems.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <h3>No saved items yet</h3>
                <p>Tap the bookmark icon on any letter or insight to save it here.</p>
            </div>
        `;
        return;
    }

    container.innerHTML = '';
    appState.savedItems.forEach(item => {
        const savedCard = createSavedCard(item);
        container.appendChild(savedCard);
    });
}

function createSavedCard(item) {
    const card = document.createElement('div');
    card.className = item.type === 'letter' ? 'letter-card' : 'insight-card';

    if (item.type === 'letter') {
        const letter = appState.allLetters.find(l => l.id === item.id);
        if (letter) {
            card.onclick = () => openLetterModal(letter);
            card.innerHTML = `
                <div class="letter-header">
                    <span class="letter-year">${letter.year}</span>
                    <span class="letter-id">#${letter.letterId}</span>
                </div>
                <div class="letter-preview">${letter.preview}</div>
                <div class="letter-meta">
                    <span class="letter-category">${letter.category}</span>
                    <span class="letter-size">${formatFileSize(letter.size)}</span>
                </div>
            `;
        }
    } else {
        const insight = appState.allInsights.find(i => i.id === item.id);
        if (insight) {
            card.innerHTML = `
                <div class="insight-header">
                    <div class="insight-date">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                            <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        <span>${insight.date}</span>
                    </div>
                    <div class="insight-actions">
                        <button class="icon-btn" onclick="toggleSaveInsight(${insight.id}, event)">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="insight-text">${insight.text}</div>
            `;
        }
    }

    return card;
}

// Modal Functions
function openLetterModal(letter) {
    const modal = document.getElementById('letterModal');
    const title = document.getElementById('modalTitle');
    const body = document.getElementById('modalBody');

    title.textContent = `Letter ${letter.year}/${letter.letterId}`;

    body.innerHTML = `
        <div class="letter-details">
            <div class="detail-row">
                <span class="detail-label">Year:</span>
                <span class="detail-value">${letter.year} (${letter.gregorianYear})</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Letter ID:</span>
                <span class="detail-value">#${letter.letterId}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Category:</span>
                <span class="detail-value">${letter.category}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">File Size:</span>
                <span class="detail-value">${formatFileSize(letter.size)}</span>
            </div>
        </div>
        <div class="letter-content">
            <h4>Letter Preview:</h4>
            <div class="preview-text">${letter.preview}</div>
            <div class="pdf-placeholder">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" fill="currentColor"/>
                    <path d="M14 2V8H20" stroke="white" stroke-width="2"/>
                </svg>
                <p>Click below to open the original PDF letter</p>
                <button class="pdf-btn" onclick="window.open('${letter.downloadUrl}', '_blank')">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2"/>
                        <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 15V3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Open PDF Letter
                </button>
            </div>
        </div>
    `;

    // Setup modal action buttons
    setupModalActions(letter);

    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeModal() {
    const modal = document.getElementById('letterModal');
    modal.classList.remove('active');
    document.body.style.overflow = 'auto';
}

function setupModalActions(letter) {
    const saveBtn = document.getElementById('saveLetterBtn');
    const shareBtn = document.getElementById('shareLetterBtn');

    // Update save button state
    const isSaved = appState.savedItems.some(item => item.type === 'letter' && item.id === letter.id);
    saveBtn.querySelector('path').setAttribute('fill', isSaved ? 'currentColor' : 'none');

    // Setup click handlers
    saveBtn.onclick = (e) => toggleSaveLetter(letter.id, e);
    shareBtn.onclick = (e) => shareLetter(letter, e);
}

// Save/Share Functions
function toggleSaveLetter(letterId, event) {
    event.stopPropagation();

    const existingIndex = appState.savedItems.findIndex(item => item.type === 'letter' && item.id === letterId);

    if (existingIndex >= 0) {
        // Remove from saved
        appState.savedItems.splice(existingIndex, 1);
    } else {
        // Add to saved
        appState.savedItems.push({
            type: 'letter',
            id: letterId,
            savedAt: new Date().toISOString()
        });
    }

    // Update localStorage
    localStorage.setItem('savedItems', JSON.stringify(appState.savedItems));

    // Update UI
    updateSavedStates();
    updateCounters();

    if (appState.currentTab === 'saved') {
        renderSaved();
    }
}

function toggleSaveInsight(insightId, event) {
    event.stopPropagation();

    const existingIndex = appState.savedItems.findIndex(item => item.type === 'insight' && item.id === insightId);

    if (existingIndex >= 0) {
        // Remove from saved
        appState.savedItems.splice(existingIndex, 1);
    } else {
        // Add to saved
        appState.savedItems.push({
            type: 'insight',
            id: insightId,
            savedAt: new Date().toISOString()
        });
    }

    // Update localStorage
    localStorage.setItem('savedItems', JSON.stringify(appState.savedItems));

    // Update UI
    updateSavedStates();
    updateCounters();

    if (appState.currentTab === 'saved') {
        renderSaved();
    } else if (appState.currentTab === 'insights') {
        renderInsights();
    }
}

function updateSavedStates() {
    // Update letter saved states
    appState.allLetters.forEach(letter => {
        letter.saved = appState.savedItems.some(item => item.type === 'letter' && item.id === letter.id);
    });

    // Update insight saved states
    appState.allInsights.forEach(insight => {
        insight.saved = appState.savedItems.some(item => item.type === 'insight' && item.id === insight.id);
    });
}

function shareLetter(letter, event) {
    event.stopPropagation();

    const shareUrl = `https://RebbeResponsa.app/letters/${letter.year}/${letter.letterId}`;
    const shareText = `Letter ${letter.year}/${letter.letterId} from the Rebbe: ${letter.preview.substring(0, 100)}...`;

    if (navigator.share) {
        navigator.share({
            title: `Rebbe Letter ${letter.year}/${letter.letterId}`,
            text: shareText,
            url: shareUrl
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(shareUrl).then(() => {
            alert('Letter URL copied to clipboard!');
        });
    }
}

function shareInsight(insightId, event) {
    event.stopPropagation();

    const insight = appState.allInsights.find(i => i.id === insightId);
    if (!insight) return;

    const shareText = `"${insight.text}" - The Rebbe (${insight.date})`;

    if (navigator.share) {
        navigator.share({
            title: `Insight from the Rebbe`,
            text: shareText
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(shareText).then(() => {
            alert('Insight copied to clipboard!');
        });
    }
}

// Additional Functions
function loadMoreLetters() {
    appState.currentPage++;
    renderLetters();
}

function searchByTopic(topicName) {
    appState.searchQuery = topicName.toLowerCase();
    document.getElementById('searchInput').value = topicName;

    if (!appState.searchActive) {
        toggleSearch();
    }

    switchTab('letters');
    applyFilters();
}
