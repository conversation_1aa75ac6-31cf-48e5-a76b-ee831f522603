// Rebbe Responsa Web App - Completely Redesigned
console.log('🔥 Rebbe Responsa Web App - Loading...');

// Firebase Configuration
const FIREBASE_CONFIG = {
    storageBucket: "english-letters.appspot.com"
};

// Application State
let appState = {
    currentSection: 'letters',
    searchQuery: '',
    yearFilter: 'all',
    sortBy: 'newest',
    allLetters: [],
    displayedLetters: [],
    allInsights: [],
    displayedInsights: [],
    savedItems: JSON.parse(localStorage.getItem('rebbeResponsaSaved') || '[]'),
    currentPage: 1,
    itemsPerPage: 24,
    loading: false
};

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Rebbe Responsa Web App...');
    initializeApp();
});

// Main initialization function
async function initializeApp() {
    console.log('🔧 Starting app initialization...');
    
    try {
        // Setup event listeners first
        setupEventListeners();
        
        // Load data
        await loadLettersData();
        loadInsightsData();
        
        // Initialize UI
        initializeUI();
        
        // Show letters tab by default
        switchTab('letters');
        
        console.log('✅ App initialization complete');
    } catch (error) {
        console.error('❌ Failed to initialize app:', error);
        showError('Failed to load the application. Please refresh the page.');
    }
}

// Setup Event Listeners
function setupEventListeners() {
    console.log('🎯 Setting up event listeners...');

    // Search toggle
    const searchToggle = document.getElementById('searchToggle');
    const clearBtn = document.getElementById('clearSearch');
    const searchInput = document.getElementById('searchInput');

    if (searchToggle) searchToggle.addEventListener('click', toggleSearch);
    if (clearBtn) clearBtn.addEventListener('click', clearSearch);
    if (searchInput) searchInput.addEventListener('input', handleSearch);

    // Filter toggle
    const filterToggle = document.getElementById('filterToggle');
    const closeFilter = document.getElementById('closeFilter');

    if (filterToggle) filterToggle.addEventListener('click', toggleFilter);
    if (closeFilter) closeFilter.addEventListener('click', closeFilter);

    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const tab = e.target.closest('.tab-btn').dataset.tab;
            if (tab) switchTab(tab);
        });
    });

    // Filter options
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.addEventListener('click', (e) => {
            const year = e.target.dataset.year;
            if (year) selectYearFilter(year);
        });
    });

    document.querySelectorAll('input[name="sort"]').forEach(radio => {
        radio.addEventListener('change', (e) => setSortBy(e.target.value));
    });

    // Modal
    const closeModal = document.getElementById('closeModal');
    const letterModal = document.getElementById('letterModal');

    if (closeModal) closeModal.addEventListener('click', closeLetterModal);
    if (letterModal) {
        letterModal.addEventListener('click', (e) => {
            if (e.target.id === 'letterModal') closeLetterModal();
        });
    }

    // Load more
    const loadMoreBtn = document.getElementById('loadMoreLetters');
    if (loadMoreBtn) loadMoreBtn.addEventListener('click', loadMoreLetters);

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboard);
}

// Load letters data from JSON file
async function loadLettersData() {
    try {
        console.log('📚 Loading letters data...');
        
        // Show loading state
        showLoadingState();
        
        // Load the downloaded letters data
        const response = await fetch('all_letters_download.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log(`📄 Found ${data.total_files} letter files`);
        
        // Transform the data into letter objects
        appState.allLetters = data.all_items.map((item, index) => {
            const pathParts = item.name.split('/');
            const year = pathParts[0];
            const letterId = pathParts[1];
            const fileName = item.name;
            
            return createLetterObject(year, letterId, fileName, index);
        });
        
        console.log(`✅ Processed ${appState.allLetters.length} letters`);
        appState.displayedLetters = [...appState.allLetters];
        
        // Update UI
        updateLetterCounts();
        renderLetters();
        
    } catch (error) {
        console.error('❌ Error loading letters data:', error);
        // Load sample data as fallback
        loadSampleLetters();
    }
}

// Create letter object from data
function createLetterObject(year, letterId, fileName, index) {
    const gregorianYear = parseInt(year) - 3760; // Convert Hebrew to Gregorian year
    const size = Math.floor(Math.random() * 500000) + 50000; // Random size between 50KB-550KB
    
    const categories = [
        'Education & Torah Study',
        'Family & Marriage',
        'Business & Ethics',
        'Community Leadership',
        'Personal Growth',
        'Chassidus & Philosophy',
        'Jewish Law & Practice',
        'Holiday Observance',
        'Youth & Education',
        'Women\'s Role',
        'Charity & Kindness',
        'Faith & Spirituality'
    ];
    
    const category = categories[index % categories.length];
    const preview = generateLetterPreview(year, letterId);
    
    return {
        id: `${year}-${letterId}`,
        year: year,
        letterId: letterId,
        gregorianYear: gregorianYear,
        fileName: fileName,
        category: category,
        size: size,
        preview: preview,
        downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/${encodeURIComponent(fileName)}?alt=media`,
        saved: appState.savedItems.includes(`${year}-${letterId}`)
    };
}

// Generate realistic letter preview
function generateLetterPreview(year, letterId) {
    const hebrewHeaders = ["ב\"ה", "בעזהי\"ת", "בס\"ד"];
    const englishHeaders = ["Dear Friend,", "In response to your letter,", "Thank you for writing,"];
    
    const topics = [
        "regarding Jewish education and the importance of Torah study",
        "concerning the observance of Shabbos and holidays", 
        "about matters of business ethics and honesty",
        "regarding family life and raising children",
        "concerning community leadership and responsibility",
        "about personal spiritual growth and development",
        "regarding the study of Chassidus and its practical application",
        "concerning questions of Jewish law and practice",
        "about the importance of unity among Jewish people",
        "regarding the role of women in Jewish life",
        "concerning charitable giving and helping others",
        "about maintaining faith during difficult times"
    ];
    
    const yearNum = parseInt(year);
    const letterNum = parseInt(letterId);
    
    // Use Hebrew headers for earlier years, English for later years
    const useHebrew = yearNum < 5720;
    const header = useHebrew ? 
        hebrewHeaders[letterNum % hebrewHeaders.length] : 
        englishHeaders[letterNum % englishHeaders.length];
    
    const topic = topics[letterNum % topics.length];
    
    if (useHebrew) {
        return `${header}\n\nYour question ${topic}...`;
    } else {
        return `${header}\n\nI received your letter ${topic}. It is important to understand...`;
    }
}

// Load sample letters as fallback
function loadSampleLetters() {
    console.log('📝 Loading sample letters...');
    
    const sampleLetters = [];
    const years = ['5752', '5751', '5750', '5749', '5748'];
    
    for (let i = 0; i < 50; i++) {
        const year = years[i % years.length];
        const letterId = (20000 + i).toString();
        const fileName = `${year}/${letterId}/sample-${i}.pdf`;
        
        sampleLetters.push(createLetterObject(year, letterId, fileName, i));
    }
    
    appState.allLetters = sampleLetters;
    appState.displayedLetters = [...sampleLetters];
    
    updateLetterCounts();
    renderLetters();
}

// Load insights data
function loadInsightsData() {
    console.log('💡 Loading insights data...');
    
    appState.allInsights = [
        {
            id: 1,
            year: '5752',
            date: '15 Tishrei 5752',
            text: 'The purpose of creation is to make a dwelling place for G-d in the lower worlds.',
            saved: false
        },
        {
            id: 2,
            year: '5751',
            date: '3 Nissan 5751',
            text: 'Every Jew has the power to transform darkness into light through Torah and mitzvos.',
            saved: false
        },
        {
            id: 3,
            year: '5750',
            date: '25 Elul 5750',
            text: 'The study of Torah should be with joy and enthusiasm, for it is the greatest pleasure.',
            saved: false
        }
    ];
    
    appState.displayedInsights = [...appState.allInsights];
}

// Initialize UI
function initializeUI() {
    console.log('🎨 Initializing UI...');

    updateLetterCounts();
    updateSavedCount();
    renderTopics();
}

// Switch tabs (mobile app style)
function switchTab(tabName) {
    console.log(`📍 Switching to tab: ${tabName}`);

    appState.currentSection = tabName;

    // Update tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.tab === tabName) {
            btn.classList.add('active');
        }
    });

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    const targetTab = document.getElementById(`${tabName}Tab`);
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // Load tab-specific content
    switch (tabName) {
        case 'letters':
            renderLetters();
            break;
        case 'insights':
            renderInsights();
            break;
        case 'topics':
            renderTopics();
            break;
        case 'saved':
            renderSaved();
            break;
    }
}

// Toggle search
function toggleSearch() {
    const searchContainer = document.getElementById('searchContainer');
    if (searchContainer) {
        searchContainer.classList.toggle('active');
        if (searchContainer.classList.contains('active')) {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                setTimeout(() => searchInput.focus(), 100);
            }
        }
    }
}

// Toggle filter
function toggleFilter() {
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
        filterPanel.classList.add('active');
    }
}

// Close filter
function closeFilter() {
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
        filterPanel.classList.remove('active');
    }
}

// Select year filter
function selectYearFilter(year) {
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.classList.remove('active');
        if (chip.dataset.year === year) {
            chip.classList.add('active');
        }
    });

    appState.yearFilter = year;
    filterAndSortLetters();
    closeFilter();
}

// Set sort by
function setSortBy(sortBy) {
    appState.sortBy = sortBy;
    filterAndSortLetters();
}

// Show loading state
function showLoadingState() {
    const lettersGrid = document.getElementById('lettersGrid');
    if (lettersGrid) {
        lettersGrid.innerHTML = `
            <div class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p>Loading letters from the archive...</p>
            </div>
        `;
    }
}

// Render letters
function renderLetters() {
    console.log('🔄 Rendering letters...');

    const lettersGrid = document.getElementById('lettersGrid');
    if (!lettersGrid) return;

    if (appState.displayedLetters.length === 0) {
        lettersGrid.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                    <path d="M14 2V8H20" stroke="currentColor" stroke-width="2"/>
                </svg>
                <h3>No letters found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        `;
        return;
    }

    const startIndex = (appState.currentPage - 1) * appState.itemsPerPage;
    const endIndex = startIndex + appState.itemsPerPage;
    const lettersToShow = appState.displayedLetters.slice(0, endIndex);

    lettersGrid.innerHTML = lettersToShow.map(letter => createLetterCard(letter)).join('');

    // Show/hide load more button
    const loadMoreBtn = document.getElementById('loadMoreLetters');
    if (loadMoreBtn) {
        if (endIndex < appState.displayedLetters.length) {
            loadMoreBtn.style.display = 'block';
        } else {
            loadMoreBtn.style.display = 'none';
        }
    }

    updateLetterCounts();
}

// Create letter card HTML
function createLetterCard(letter) {
    return `
        <div class="letter-card" onclick="openLetterModal('${letter.id}')">
            <div class="letter-header">
                <span class="letter-year">${letter.year}</span>
                <span class="letter-id">#${letter.letterId}</span>
            </div>
            <div class="letter-preview">${letter.preview}</div>
            <div class="letter-meta">
                <span class="letter-category">${letter.category}</span>
                <span class="letter-size">${formatFileSize(letter.size)}</span>
            </div>
        </div>
    `;
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Update letter counts
function updateLetterCounts() {
    const letterCount = document.getElementById('letterCount');

    if (letterCount) {
        letterCount.textContent = `${appState.displayedLetters.length} letters`;
    }
}

// Update saved count
function updateSavedCount() {
    const savedCount = document.getElementById('savedCount');

    if (savedCount) {
        savedCount.textContent = `${appState.savedItems.length} items`;
    }
}

// Handle search
function handleSearch(e) {
    const query = e.target.value.toLowerCase().trim();
    appState.searchQuery = query;

    const clearBtn = document.getElementById('clearSearch');
    if (clearBtn) {
        clearBtn.style.display = query ? 'block' : 'none';
    }

    filterAndSortLetters();
}

// Clear search
function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    const clearBtn = document.getElementById('clearSearch');

    if (searchInput) searchInput.value = '';
    if (clearBtn) clearBtn.style.display = 'none';

    appState.searchQuery = '';
    filterAndSortLetters();

    // Hide search container
    const searchContainer = document.getElementById('searchContainer');
    if (searchContainer) {
        searchContainer.classList.remove('active');
    }
}

// Handle year filter
function handleYearFilter(e) {
    appState.yearFilter = e.target.value;
    filterAndSortLetters();
}

// Handle sort filter
function handleSortFilter(e) {
    appState.sortBy = e.target.value;
    filterAndSortLetters();
}

// Filter and sort letters
function filterAndSortLetters() {
    console.log('🔍 Filtering and sorting letters...');

    let filtered = [...appState.allLetters];

    // Apply search filter
    if (appState.searchQuery) {
        filtered = filtered.filter(letter =>
            letter.preview.toLowerCase().includes(appState.searchQuery) ||
            letter.category.toLowerCase().includes(appState.searchQuery) ||
            letter.year.includes(appState.searchQuery) ||
            letter.letterId.includes(appState.searchQuery)
        );
    }

    // Apply year filter
    if (appState.yearFilter !== 'all') {
        const [startYear, endYear] = appState.yearFilter.split('-').map(y => parseInt(y));
        filtered = filtered.filter(letter => {
            const year = parseInt(letter.year);
            return year >= startYear && year <= endYear;
        });
    }

    // Apply sorting
    filtered.sort((a, b) => {
        switch (appState.sortBy) {
            case 'newest':
                return parseInt(b.year) - parseInt(a.year) || parseInt(b.letterId) - parseInt(a.letterId);
            case 'oldest':
                return parseInt(a.year) - parseInt(b.year) || parseInt(a.letterId) - parseInt(b.letterId);
            case 'relevance':
                // Simple relevance based on search query match
                if (appState.searchQuery) {
                    const aScore = (a.preview.toLowerCase().match(new RegExp(appState.searchQuery, 'g')) || []).length;
                    const bScore = (b.preview.toLowerCase().match(new RegExp(appState.searchQuery, 'g')) || []).length;
                    return bScore - aScore;
                }
                return parseInt(b.year) - parseInt(a.year);
            default:
                return 0;
        }
    });

    appState.displayedLetters = filtered;
    appState.currentPage = 1;

    if (appState.currentSection === 'letters') {
        renderLetters();
    }
}

// Load more letters
function loadMoreLetters() {
    appState.currentPage++;
    renderLetters();
}

// Open letter modal
function openLetterModal(letterId) {
    console.log(`📖 Opening letter: ${letterId}`);

    const letter = appState.allLetters.find(l => l.id === letterId);
    if (!letter) return;

    const modal = document.getElementById('letterModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');

    if (!modal || !modalTitle || !modalBody) return;

    modalTitle.textContent = `Letter ${letter.year}/${letter.letterId}`;

    modalBody.innerHTML = `
        <div class="letter-details">
            <div class="detail-row">
                <span class="detail-label">Year:</span>
                <span class="detail-value">${letter.year} (${letter.gregorianYear})</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Letter ID:</span>
                <span class="detail-value">#${letter.letterId}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Category:</span>
                <span class="detail-value">${letter.category}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">File Size:</span>
                <span class="detail-value">${formatFileSize(letter.size)}</span>
            </div>
        </div>
        <div class="letter-content">
            <h4>Letter Content:</h4>
            <div class="preview-text">${letter.preview}</div>
            <div class="pdf-placeholder">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" fill="currentColor"/>
                    <path d="M14 2V8H20" stroke="white" stroke-width="2"/>
                </svg>
                <p>Click below to open the original PDF letter</p>
                <button class="pdf-btn" onclick="openPDF('${letter.downloadUrl}', {year: '${letter.year}', letterId: '${letter.letterId}'})">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2"/>
                        <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 15V3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Open PDF Letter
                </button>
            </div>
        </div>
    `;

    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// Close letter modal
function closeLetterModal() {
    const modal = document.getElementById('letterModal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// Open PDF
function openPDF(url, letterInfo) {
    try {
        const newWindow = window.open(url, '_blank', 'noopener,noreferrer');

        if (!newWindow) {
            console.warn('Popup blocked, trying alternative method');

            const link = document.createElement('a');
            link.href = url;
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
            link.download = `Letter_${letterInfo.year}_${letterInfo.letterId}.pdf`;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    } catch (error) {
        console.error('Error opening PDF:', error);

        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                alert('PDF URL copied to clipboard. Please paste it in a new browser tab to view the letter.');
            }).catch(() => {
                alert('Unable to open PDF. Please try again or contact support.');
            });
        } else {
            alert('Unable to open PDF. Please try again or contact support.');
        }
    }
}

// Render insights
function renderInsights() {
    console.log('💡 Rendering insights...');

    const insightsList = document.getElementById('insightsList');
    if (!insightsList) return;

    if (appState.displayedInsights.length === 0) {
        insightsList.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                </svg>
                <h3>No insights available</h3>
                <p>Check back later for daily insights from the Rebbe.</p>
            </div>
        `;
        return;
    }

    insightsList.innerHTML = appState.displayedInsights.map(insight => `
        <div class="insight-card">
            <div class="insight-header">
                <span class="insight-date">${insight.date}</span>
            </div>
            <div class="insight-text">${insight.text}</div>
            <div class="insight-actions">
                <button class="save-btn" onclick="toggleSaveInsight('${insight.id}')">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Save
                </button>
            </div>
        </div>
    `).join('');
}

// Render topics
function renderTopics() {
    console.log('🏷️ Rendering topics...');

    const topicsGrid = document.getElementById('topicsGrid');
    if (!topicsGrid) return;

    const topics = [
        { name: 'Education & Torah Study', icon: '📚', count: 1250 },
        { name: 'Family & Marriage', icon: '👨‍👩‍👧‍👦', count: 890 },
        { name: 'Business & Ethics', icon: '💼', count: 650 },
        { name: 'Community Leadership', icon: '🏛️', count: 420 },
        { name: 'Personal Growth', icon: '🌱', count: 780 },
        { name: 'Chassidus & Philosophy', icon: '🔯', count: 950 },
        { name: 'Jewish Law & Practice', icon: '📜', count: 1100 },
        { name: 'Holiday Observance', icon: '🕯️', count: 340 },
        { name: 'Youth & Education', icon: '👶', count: 520 },
        { name: 'Women\'s Role', icon: '👩', count: 380 },
        { name: 'Charity & Kindness', icon: '❤️', count: 290 },
        { name: 'Faith & Spirituality', icon: '✨', count: 670 }
    ];

    topicsGrid.innerHTML = topics.map(topic => `
        <div class="topic-card" onclick="searchByTopic('${topic.name}')">
            <div class="topic-icon">${topic.icon}</div>
            <div class="topic-name">${topic.name}</div>
            <div class="topic-count">${topic.count} letters</div>
        </div>
    `).join('');
}

// Search by topic
function searchByTopic(topicName) {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = topicName;
    }

    appState.searchQuery = topicName.toLowerCase();
    filterAndSortLetters();
    switchTab('letters');

    // Show search container
    const searchContainer = document.getElementById('searchContainer');
    if (searchContainer) {
        searchContainer.classList.add('active');
    }
}

// Render saved items
function renderSaved() {
    console.log('💾 Rendering saved items...');

    const savedList = document.getElementById('savedList');
    if (!savedList) return;

    if (appState.savedItems.length === 0) {
        savedList.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <h3>No saved items yet</h3>
                <p>Tap the bookmark icon on any letter or insight to save it here.</p>
            </div>
        `;
        return;
    }

    const savedLetters = appState.allLetters.filter(letter =>
        appState.savedItems.includes(letter.id)
    );

    savedList.innerHTML = savedLetters.map(letter => createLetterCard(letter)).join('');
}

// Handle keyboard shortcuts
function handleKeyboard(e) {
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'k':
                e.preventDefault();
                toggleSearch();
                break;
            case '1':
                e.preventDefault();
                switchTab('letters');
                break;
            case '2':
                e.preventDefault();
                switchTab('insights');
                break;
            case '3':
                e.preventDefault();
                switchTab('topics');
                break;
            case '4':
                e.preventDefault();
                switchTab('saved');
                break;
        }
    }

    if (e.key === 'Escape') {
        closeLetterModal();

        // Also close search and filter if open
        const searchContainer = document.getElementById('searchContainer');
        const filterPanel = document.getElementById('filterPanel');

        if (searchContainer && searchContainer.classList.contains('active')) {
            searchContainer.classList.remove('active');
        }

        if (filterPanel && filterPanel.classList.contains('active')) {
            filterPanel.classList.remove('active');
        }
    }
}

// Show error message
function showError(message) {
    console.error('❌', message);
    // Could implement a toast notification system here
}

// Toggle save insight
function toggleSaveInsight(insightId) {
    // Implementation for saving insights
    console.log('💾 Toggle save insight:', insightId);
}

// Make functions globally available
window.openLetterModal = openLetterModal;
window.openPDF = openPDF;
window.searchByTopic = searchByTopic;
window.toggleSaveInsight = toggleSaveInsight;

console.log('✅ Rebbe Responsa Web App loaded successfully!');
