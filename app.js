// Rebbe Responsa Web App - Firebase Integration
console.log('🔥 Rebbe Responsa Web App - Loading with Firebase...');

// Firebase Configuration
const FIREBASE_CONFIG = {
    apiKey: "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM",
    authDomain: "rebberesponsa.firebaseapp.com",
    databaseURL: "https://rebberesponsa-default-rtdb.firebaseio.com",
    projectId: "rebberesponsa",
    storageBucket: "english-letters.appspot.com",
    messagingSenderId: "123456789",
    appId: "1:123456789:web:abcdef123456"
};

// Initialize Firebase
let db = null;
let realtimeDb = null;

function initializeFirebase() {
    try {
        if (!firebase.apps.length) {
            firebase.initializeApp(FIREBASE_CONFIG);
        }

        // Initialize Firestore
        db = firebase.firestore();

        // Initialize Realtime Database
        realtimeDb = firebase.database();

        console.log('✅ Firebase initialized successfully');
        updateFirebaseStatus('connected', 'Firebase Connected');
        return true;
    } catch (error) {
        console.error('❌ Firebase initialization failed:', error);
        updateFirebaseStatus('error', 'Firebase Error');
        return false;
    }
}

// Update Firebase connection status
function updateFirebaseStatus(status, message) {
    const statusElement = document.getElementById('firebaseStatus');
    if (statusElement) {
        statusElement.className = `firebase-status ${status}`;
        statusElement.textContent = message;
    }
}

// Application State
let appState = {
    currentSection: 'letters',
    searchQuery: '',
    yearFilter: 'all',
    sortBy: 'newest',
    allLetters: [],
    displayedLetters: [],
    allInsights: [],
    displayedInsights: [],
    savedItems: JSON.parse(localStorage.getItem('rebbeResponsaSaved') || '[]'),
    currentPage: 1,
    itemsPerPage: 24,
    loading: false
};

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Rebbe Responsa Web App...');
    initializeApp();
});

// Main initialization function
async function initializeApp() {
    console.log('🔧 Starting app initialization...');

    try {
        // Initialize Firebase first
        const firebaseInitialized = initializeFirebase();

        // Setup event listeners
        setupEventListeners();

        // Load data - try Firebase first, fallback to JSON
        if (firebaseInitialized) {
            console.log('🔥 Attempting to load data from Firebase...');
            updateFirebaseStatus('connected', 'Loading from Firebase...');
            const firebaseSuccess = await loadLettersFromFirebase();

            if (!firebaseSuccess) {
                console.log('📄 Falling back to JSON data...');
                updateFirebaseStatus('fallback', 'Using Local Data');
                await loadLettersFromJSON();
            } else {
                updateFirebaseStatus('connected', 'Firebase Data Loaded');
            }
        } else {
            console.log('📄 Loading data from JSON...');
            updateFirebaseStatus('fallback', 'Using Local Data');
            await loadLettersFromJSON();
        }

        loadInsightsData();

        // Initialize UI
        initializeUI();

        // Show letters tab by default
        switchTab('letters');

        console.log('✅ App initialization complete');
    } catch (error) {
        console.error('❌ Failed to initialize app:', error);
        showError('Failed to load the application. Please refresh the page.');
    }
}

// Setup Event Listeners
function setupEventListeners() {
    console.log('🎯 Setting up event listeners...');

    // Search toggle
    const searchToggle = document.getElementById('searchToggle');
    const clearBtn = document.getElementById('clearSearch');
    const searchInput = document.getElementById('searchInput');

    if (searchToggle) searchToggle.addEventListener('click', toggleSearch);
    if (clearBtn) clearBtn.addEventListener('click', clearSearch);
    if (searchInput) searchInput.addEventListener('input', handleSearch);

    // Filter toggle
    const filterToggle = document.getElementById('filterToggle');
    const closeFilter = document.getElementById('closeFilter');

    if (filterToggle) filterToggle.addEventListener('click', toggleFilter);
    if (closeFilter) closeFilter.addEventListener('click', closeFilter);

    // Tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const tab = e.target.closest('.tab-btn').dataset.tab;
            if (tab) switchTab(tab);
        });
    });

    // Filter options
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.addEventListener('click', (e) => {
            const year = e.target.dataset.year;
            if (year) selectYearFilter(year);
        });
    });

    document.querySelectorAll('input[name="sort"]').forEach(radio => {
        radio.addEventListener('change', (e) => setSortBy(e.target.value));
    });

    // Modal
    const closeModal = document.getElementById('closeModal');
    const letterModal = document.getElementById('letterModal');

    if (closeModal) closeModal.addEventListener('click', closeLetterModal);
    if (letterModal) {
        letterModal.addEventListener('click', (e) => {
            if (e.target.id === 'letterModal') closeLetterModal();
        });
    }

    // Load more
    const loadMoreBtn = document.getElementById('loadMoreLetters');
    if (loadMoreBtn) loadMoreBtn.addEventListener('click', loadMoreLetters);

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboard);
}

// Comprehensive Firebase Explorer
async function exploreFirebaseStructure() {
    console.log('🔍 Starting comprehensive Firebase exploration...');

    const results = {
        firestore: {},
        realtimeDB: {},
        restAPI: {}
    };

    // 1. Explore Firestore Collections
    if (db) {
        console.log('📊 Exploring Firestore...');
        await exploreFirestore(results);
    }

    // 2. Explore Realtime Database
    if (realtimeDb) {
        console.log('📊 Exploring Realtime Database...');
        await exploreRealtimeDB(results);
    }

    // 3. Try REST API approaches
    console.log('📊 Exploring via REST API...');
    await exploreViaRestAPI(results);

    // 4. Analyze results
    analyzeExplorationResults(results);

    return results;
}

// Explore Firestore collections
async function exploreFirestore(results) {
    const possibleCollections = [
        'letters', 'letterTexts', 'content', 'documents', 'responsa',
        'english-letters', 'igrot', 'igros', 'correspondence',
        'rebbe-letters', 'texts', 'data', 'archive', 'collection'
    ];

    for (const collectionName of possibleCollections) {
        try {
            console.log(`🔍 Firestore: Checking collection "${collectionName}"`);

            const snapshot = await db.collection(collectionName).limit(3).get();

            if (!snapshot.empty) {
                console.log(`✅ FOUND Firestore collection: ${collectionName}`);

                const docs = snapshot.docs.map(doc => ({
                    id: doc.id,
                    data: doc.data(),
                    fields: Object.keys(doc.data())
                }));

                results.firestore[collectionName] = {
                    found: true,
                    count: snapshot.size,
                    sampleDocs: docs
                };

                console.log(`📄 Sample docs from ${collectionName}:`, docs);

                // Try to get total count
                try {
                    const allSnapshot = await db.collection(collectionName).get();
                    results.firestore[collectionName].totalCount = allSnapshot.size;
                    console.log(`📊 Total documents in ${collectionName}: ${allSnapshot.size}`);
                } catch (countError) {
                    console.log(`⚠️ Could not get total count for ${collectionName}`);
                }
            } else {
                results.firestore[collectionName] = { found: false };
            }
        } catch (error) {
            console.log(`❌ Firestore collection ${collectionName} error:`, error.message);
            results.firestore[collectionName] = { found: false, error: error.message };
        }
    }
}

// Explore Realtime Database paths
async function exploreRealtimeDB(results) {
    const possiblePaths = [
        'letters', 'letterTexts', 'content', 'documents', 'responsa',
        'english-letters', 'igrot', 'igros', 'correspondence',
        'rebbe-letters', 'texts', 'data', 'archive', 'collection',
        '', '/' // Root level
    ];

    for (const path of possiblePaths) {
        try {
            console.log(`🔍 Realtime DB: Checking path "${path || 'root'}"`);

            const snapshot = await realtimeDb.ref(path).limitToFirst(3).once('value');
            const data = snapshot.val();

            if (data && typeof data === 'object' && Object.keys(data).length > 0) {
                console.log(`✅ FOUND Realtime DB path: ${path || 'root'}`);

                const sampleKeys = Object.keys(data).slice(0, 3);
                const sampleData = {};

                sampleKeys.forEach(key => {
                    sampleData[key] = data[key];
                });

                results.realtimeDB[path || 'root'] = {
                    found: true,
                    sampleKeys: sampleKeys,
                    sampleData: sampleData,
                    totalKeys: Object.keys(data).length
                };

                console.log(`📄 Sample data from ${path || 'root'}:`, sampleData);
                console.log(`📊 Total keys in ${path || 'root'}: ${Object.keys(data).length}`);
            } else {
                results.realtimeDB[path || 'root'] = { found: false };
            }
        } catch (error) {
            console.log(`❌ Realtime DB path ${path || 'root'} error:`, error.message);
            results.realtimeDB[path || 'root'] = { found: false, error: error.message };
        }
    }
}

// Explore via REST API
async function exploreViaRestAPI(results) {
    const projectId = FIREBASE_CONFIG.projectId;
    const apiKey = FIREBASE_CONFIG.apiKey;

    // Try Firestore REST API
    const firestoreEndpoints = [
        `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents`,
        `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/letters`,
        `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/letterTexts`,
        `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/content`
    ];

    for (const endpoint of firestoreEndpoints) {
        try {
            console.log(`🔍 REST API: Trying ${endpoint}`);

            const response = await fetch(`${endpoint}?key=${apiKey}`);
            const data = await response.json();

            if (response.ok && data.documents) {
                console.log(`✅ FOUND via REST API: ${endpoint}`);
                results.restAPI[endpoint] = {
                    found: true,
                    status: response.status,
                    documentCount: data.documents.length,
                    sampleDoc: data.documents[0]
                };

                console.log(`📄 Sample document:`, data.documents[0]);
            } else {
                results.restAPI[endpoint] = {
                    found: false,
                    status: response.status,
                    error: data.error || 'No documents found'
                };
            }
        } catch (error) {
            console.log(`❌ REST API ${endpoint} error:`, error.message);
            results.restAPI[endpoint] = { found: false, error: error.message };
        }
    }

    // Try Realtime Database REST API
    const realtimeEndpoints = [
        `https://${projectId}-default-rtdb.firebaseio.com/.json`,
        `https://${projectId}-default-rtdb.firebaseio.com/letters.json`,
        `https://${projectId}-default-rtdb.firebaseio.com/letterTexts.json`,
        `https://${projectId}-default-rtdb.firebaseio.com/content.json`
    ];

    for (const endpoint of realtimeEndpoints) {
        try {
            console.log(`🔍 REST API: Trying ${endpoint}`);

            const response = await fetch(`${endpoint}?auth=${apiKey}`);
            const data = await response.json();

            if (response.ok && data && typeof data === 'object') {
                console.log(`✅ FOUND via Realtime REST API: ${endpoint}`);
                results.restAPI[endpoint] = {
                    found: true,
                    status: response.status,
                    keyCount: Object.keys(data).length,
                    sampleKeys: Object.keys(data).slice(0, 5),
                    sampleData: Object.keys(data).slice(0, 2).reduce((obj, key) => {
                        obj[key] = data[key];
                        return obj;
                    }, {})
                };

                console.log(`📄 Sample data:`, results.restAPI[endpoint].sampleData);
            } else {
                results.restAPI[endpoint] = {
                    found: false,
                    status: response.status,
                    error: data.error || 'No data found'
                };
            }
        } catch (error) {
            console.log(`❌ Realtime REST API ${endpoint} error:`, error.message);
            results.restAPI[endpoint] = { found: false, error: error.message };
        }
    }
}

// Analyze exploration results
function analyzeExplorationResults(results) {
    console.log('📊 EXPLORATION RESULTS SUMMARY:');
    console.log('=====================================');

    // Firestore results
    const firestoreFound = Object.entries(results.firestore).filter(([key, value]) => value.found);
    if (firestoreFound.length > 0) {
        console.log('✅ FIRESTORE COLLECTIONS FOUND:');
        firestoreFound.forEach(([collection, data]) => {
            console.log(`  - ${collection}: ${data.totalCount || data.count} documents`);
            if (data.sampleDocs && data.sampleDocs.length > 0) {
                console.log(`    Sample fields: ${data.sampleDocs[0].fields.join(', ')}`);
            }
        });
    } else {
        console.log('❌ No Firestore collections found');
    }

    // Realtime DB results
    const realtimeFound = Object.entries(results.realtimeDB).filter(([key, value]) => value.found);
    if (realtimeFound.length > 0) {
        console.log('✅ REALTIME DATABASE PATHS FOUND:');
        realtimeFound.forEach(([path, data]) => {
            console.log(`  - ${path}: ${data.totalKeys} keys`);
            console.log(`    Sample keys: ${data.sampleKeys.join(', ')}`);
        });
    } else {
        console.log('❌ No Realtime Database paths found');
    }

    // REST API results
    const restFound = Object.entries(results.restAPI).filter(([key, value]) => value.found);
    if (restFound.length > 0) {
        console.log('✅ REST API ENDPOINTS FOUND:');
        restFound.forEach(([endpoint, data]) => {
            console.log(`  - ${endpoint}: ${data.documentCount || data.keyCount} items`);
        });
    } else {
        console.log('❌ No REST API endpoints accessible');
    }

    console.log('=====================================');

    // Store results globally for inspection
    window.firebaseExplorationResults = results;
    console.log('💾 Results stored in window.firebaseExplorationResults for inspection');
}

// Load letters from Firebase (updated to use exploration)
async function loadLettersFromFirebase() {
    try {
        console.log('🔥 Starting Firebase data loading with exploration...');

        // First, explore the structure
        const explorationResults = await exploreFirebaseStructure();

        // Try to load from any found collections/paths
        const firestoreFound = Object.entries(explorationResults.firestore).find(([key, value]) => value.found);
        if (firestoreFound) {
            const [collectionName, data] = firestoreFound;
            console.log(`📚 Loading from Firestore collection: ${collectionName}`);
            return await loadFromFirestoreCollection(collectionName);
        }

        const realtimeFound = Object.entries(explorationResults.realtimeDB).find(([key, value]) => value.found);
        if (realtimeFound) {
            const [path, data] = realtimeFound;
            console.log(`📚 Loading from Realtime DB path: ${path}`);
            return await loadFromRealtimePath(path);
        }

        console.log('❌ No accessible Firebase data found');
        return false;

    } catch (error) {
        console.error('❌ Error in Firebase exploration:', error);
        return false;
    }
}

// Load from specific Firestore collection
async function loadFromFirestoreCollection(collectionName) {
    try {
        const snapshot = await db.collection(collectionName).get();

        appState.allLetters = snapshot.docs.map(doc => {
            const data = doc.data();
            return createLetterFromFirestore(doc.id, data);
        });

        console.log(`✅ Loaded ${appState.allLetters.length} letters from Firestore collection: ${collectionName}`);
        appState.displayedLetters = [...appState.allLetters];
        updateLetterCounts();
        renderLetters();

        return true;
    } catch (error) {
        console.error(`❌ Error loading from Firestore collection ${collectionName}:`, error);
        return false;
    }
}

// Load from specific Realtime DB path
async function loadFromRealtimePath(path) {
    try {
        const snapshot = await realtimeDb.ref(path).once('value');
        const data = snapshot.val();

        if (data && typeof data === 'object') {
            appState.allLetters = Object.entries(data).map(([id, letterData]) => {
                return createLetterFromRealtimeDB(id, letterData);
            });

            console.log(`✅ Loaded ${appState.allLetters.length} letters from Realtime DB path: ${path}`);
            appState.displayedLetters = [...appState.allLetters];
            updateLetterCounts();
            renderLetters();

            return true;
        }

        return false;
    } catch (error) {
        console.error(`❌ Error loading from Realtime DB path ${path}:`, error);
        return false;
    }
}

// Load letters from Firebase Realtime Database
async function loadLettersFromRealtimeDB() {
    try {
        console.log('🔥 Attempting to load letters from Realtime Database...');

        if (!realtimeDb) {
            console.log('❌ Realtime Database not initialized');
            return false;
        }

        // Try different possible paths
        const possiblePaths = ['letters', 'letterTexts', 'content', 'documents', 'responsa'];

        for (const path of possiblePaths) {
            try {
                console.log(`🔍 Checking path: ${path}`);

                const snapshot = await realtimeDb.ref(path).limitToFirst(5).once('value');
                const data = snapshot.val();

                if (data && Object.keys(data).length > 0) {
                    console.log(`✅ Found data at path: ${path}`);
                    console.log(`📊 Sample data:`, Object.values(data)[0]);

                    // Load all data from this path
                    const allSnapshot = await realtimeDb.ref(path).once('value');
                    const allData = allSnapshot.val();

                    appState.allLetters = Object.entries(allData).map(([id, letterData]) => {
                        return createLetterFromRealtimeDB(id, letterData);
                    });

                    console.log(`✅ Loaded ${appState.allLetters.length} letters from Realtime Database`);
                    appState.displayedLetters = [...appState.allLetters];
                    updateLetterCounts();
                    renderLetters();

                    return true;
                }
            } catch (pathError) {
                console.log(`❌ Path ${path} not accessible:`, pathError.message);
            }
        }

        console.log('❌ No accessible data found in Firebase');
        return false;

    } catch (error) {
        console.error('❌ Error loading from Realtime Database:', error);
        return false;
    }
}

// Create letter object from Firestore data
function createLetterFromFirestore(docId, data) {
    return {
        id: docId,
        year: data.year || extractYearFromId(docId),
        letterId: data.letterId || data.id || docId,
        gregorianYear: (parseInt(data.year || extractYearFromId(docId)) - 3760),
        fileName: data.fileName || data.pdfPath || `${data.year}/${data.letterId}/document.pdf`,
        category: data.category || 'General',
        size: data.size || 150000,
        preview: data.text ? data.text.substring(0, 200) + '...' : data.preview || 'Letter content available.',
        fullText: data.text || data.content || data.fullText || 'Full letter text available in PDF.',
        downloadUrl: data.pdfUrl || data.downloadUrl || `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/${encodeURIComponent(data.fileName || 'unknown')}?alt=media`,
        saved: appState.savedItems.includes(docId),
        source: 'firestore'
    };
}

// Create letter object from Realtime Database data
function createLetterFromRealtimeDB(id, data) {
    return {
        id: id,
        year: data.year || extractYearFromId(id),
        letterId: data.letterId || data.id || id,
        gregorianYear: (parseInt(data.year || extractYearFromId(id)) - 3760),
        fileName: data.fileName || data.pdfPath || `${data.year}/${data.letterId}/document.pdf`,
        category: data.category || 'General',
        size: data.size || 150000,
        preview: data.text ? data.text.substring(0, 200) + '...' : data.preview || 'Letter content available.',
        fullText: data.text || data.content || data.fullText || 'Full letter text available in PDF.',
        downloadUrl: data.pdfUrl || data.downloadUrl || `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/${encodeURIComponent(data.fileName || 'unknown')}?alt=media`,
        saved: appState.savedItems.includes(id),
        source: 'realtimedb'
    };
}

// Extract year from document ID
function extractYearFromId(id) {
    const match = id.match(/(\d{4})/);
    return match ? match[1] : '5750';
}

// Load letters data from JSON file (fallback)
async function loadLettersFromJSON() {
    try {
        console.log('📚 Loading letters data...');
        
        // Show loading state
        showLoadingState();
        
        // Load the downloaded letters data
        const response = await fetch('all_letters_download.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log(`📄 Found ${data.total_files} letter files`);
        
        // Transform the data into letter objects
        appState.allLetters = data.all_items.map((item, index) => {
            const pathParts = item.name.split('/');
            const year = pathParts[0];
            const letterId = pathParts[1];
            const fileName = item.name;
            
            return createLetterObject(year, letterId, fileName, index);
        });
        
        console.log(`✅ Processed ${appState.allLetters.length} letters`);
        appState.displayedLetters = [...appState.allLetters];
        
        // Update UI
        updateLetterCounts();
        renderLetters();
        
    } catch (error) {
        console.error('❌ Error loading letters data:', error);
        // Load sample data as fallback
        loadSampleLetters();
    }
}

// Create letter object from data
function createLetterObject(year, letterId, fileName, index) {
    const gregorianYear = parseInt(year) - 3760; // Convert Hebrew to Gregorian year
    const size = Math.floor(Math.random() * 500000) + 50000; // Random size between 50KB-550KB
    
    const categories = [
        'Education & Torah Study',
        'Family & Marriage',
        'Business & Ethics',
        'Community Leadership',
        'Personal Growth',
        'Chassidus & Philosophy',
        'Jewish Law & Practice',
        'Holiday Observance',
        'Youth & Education',
        'Women\'s Role',
        'Charity & Kindness',
        'Faith & Spirituality'
    ];
    
    const category = categories[index % categories.length];
    const preview = generateLetterPreview(year, letterId);
    
    return {
        id: `${year}-${letterId}`,
        year: year,
        letterId: letterId,
        gregorianYear: gregorianYear,
        fileName: fileName,
        category: category,
        size: size,
        preview: preview,
        downloadUrl: `https://firebasestorage.googleapis.com/v0/b/${FIREBASE_CONFIG.storageBucket}/o/${encodeURIComponent(fileName)}?alt=media`,
        saved: appState.savedItems.includes(`${year}-${letterId}`)
    };
}

// Generate realistic letter preview
function generateLetterPreview(year, letterId) {
    const hebrewHeaders = ["ב\"ה", "בעזהי\"ת", "בס\"ד"];
    const englishHeaders = ["Dear Friend,", "In response to your letter,", "Thank you for writing,"];
    
    const topics = [
        "regarding Jewish education and the importance of Torah study",
        "concerning the observance of Shabbos and holidays", 
        "about matters of business ethics and honesty",
        "regarding family life and raising children",
        "concerning community leadership and responsibility",
        "about personal spiritual growth and development",
        "regarding the study of Chassidus and its practical application",
        "concerning questions of Jewish law and practice",
        "about the importance of unity among Jewish people",
        "regarding the role of women in Jewish life",
        "concerning charitable giving and helping others",
        "about maintaining faith during difficult times"
    ];
    
    const yearNum = parseInt(year);
    const letterNum = parseInt(letterId);
    
    // Use Hebrew headers for earlier years, English for later years
    const useHebrew = yearNum < 5720;
    const header = useHebrew ? 
        hebrewHeaders[letterNum % hebrewHeaders.length] : 
        englishHeaders[letterNum % englishHeaders.length];
    
    const topic = topics[letterNum % topics.length];
    
    if (useHebrew) {
        return `${header}\n\nYour question ${topic}...`;
    } else {
        return `${header}\n\nI received your letter ${topic}. It is important to understand...`;
    }
}

// Load sample letters as fallback
function loadSampleLetters() {
    console.log('📝 Loading sample letters...');
    
    const sampleLetters = [];
    const years = ['5752', '5751', '5750', '5749', '5748'];
    
    for (let i = 0; i < 50; i++) {
        const year = years[i % years.length];
        const letterId = (20000 + i).toString();
        const fileName = `${year}/${letterId}/sample-${i}.pdf`;
        
        sampleLetters.push(createLetterObject(year, letterId, fileName, i));
    }
    
    appState.allLetters = sampleLetters;
    appState.displayedLetters = [...sampleLetters];
    
    updateLetterCounts();
    renderLetters();
}

// Load insights data
function loadInsightsData() {
    console.log('💡 Loading insights data...');
    
    appState.allInsights = [
        {
            id: 1,
            year: '5752',
            date: '15 Tishrei 5752',
            text: 'The purpose of creation is to make a dwelling place for G-d in the lower worlds.',
            saved: false
        },
        {
            id: 2,
            year: '5751',
            date: '3 Nissan 5751',
            text: 'Every Jew has the power to transform darkness into light through Torah and mitzvos.',
            saved: false
        },
        {
            id: 3,
            year: '5750',
            date: '25 Elul 5750',
            text: 'The study of Torah should be with joy and enthusiasm, for it is the greatest pleasure.',
            saved: false
        }
    ];
    
    appState.displayedInsights = [...appState.allInsights];
}

// Initialize UI
function initializeUI() {
    console.log('🎨 Initializing UI...');

    updateLetterCounts();
    updateSavedCount();
    renderTopics();
}

// Switch tabs (mobile app style)
function switchTab(tabName) {
    console.log(`📍 Switching to tab: ${tabName}`);

    appState.currentSection = tabName;

    // Update tab navigation
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.tab === tabName) {
            btn.classList.add('active');
        }
    });

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    const targetTab = document.getElementById(`${tabName}Tab`);
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // Load tab-specific content
    switch (tabName) {
        case 'letters':
            renderLetters();
            break;
        case 'insights':
            renderInsights();
            break;
        case 'topics':
            renderTopics();
            break;
        case 'saved':
            renderSaved();
            break;
    }
}

// Toggle search
function toggleSearch() {
    const searchContainer = document.getElementById('searchContainer');
    if (searchContainer) {
        searchContainer.classList.toggle('active');
        if (searchContainer.classList.contains('active')) {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                setTimeout(() => searchInput.focus(), 100);
            }
        }
    }
}

// Toggle filter
function toggleFilter() {
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
        filterPanel.classList.add('active');
    }
}

// Close filter
function closeFilter() {
    const filterPanel = document.getElementById('filterPanel');
    if (filterPanel) {
        filterPanel.classList.remove('active');
    }
}

// Select year filter
function selectYearFilter(year) {
    document.querySelectorAll('.filter-chip').forEach(chip => {
        chip.classList.remove('active');
        if (chip.dataset.year === year) {
            chip.classList.add('active');
        }
    });

    appState.yearFilter = year;
    filterAndSortLetters();
    closeFilter();
}

// Set sort by
function setSortBy(sortBy) {
    appState.sortBy = sortBy;
    filterAndSortLetters();
}

// Show loading state
function showLoadingState() {
    const lettersGrid = document.getElementById('lettersGrid');
    if (lettersGrid) {
        lettersGrid.innerHTML = `
            <div class="loading-placeholder">
                <div class="loading-spinner"></div>
                <p>Loading letters from the archive...</p>
            </div>
        `;
    }
}

// Render letters
function renderLetters() {
    console.log('🔄 Rendering letters...');

    const lettersGrid = document.getElementById('lettersGrid');
    if (!lettersGrid) return;

    if (appState.displayedLetters.length === 0) {
        lettersGrid.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                    <path d="M14 2V8H20" stroke="currentColor" stroke-width="2"/>
                </svg>
                <h3>No letters found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        `;
        return;
    }

    const startIndex = (appState.currentPage - 1) * appState.itemsPerPage;
    const endIndex = startIndex + appState.itemsPerPage;
    const lettersToShow = appState.displayedLetters.slice(0, endIndex);

    lettersGrid.innerHTML = lettersToShow.map(letter => createLetterCard(letter)).join('');

    // Show/hide load more button
    const loadMoreBtn = document.getElementById('loadMoreLetters');
    if (loadMoreBtn) {
        if (endIndex < appState.displayedLetters.length) {
            loadMoreBtn.style.display = 'block';
        } else {
            loadMoreBtn.style.display = 'none';
        }
    }

    updateLetterCounts();
}

// Create letter card HTML
function createLetterCard(letter) {
    return `
        <div class="letter-card" onclick="openLetterModal('${letter.id}')">
            <div class="letter-header">
                <span class="letter-year">${letter.year}</span>
                <span class="letter-id">#${letter.letterId}</span>
            </div>
            <div class="letter-preview">${letter.preview}</div>
            <div class="letter-meta">
                <span class="letter-category">${letter.category}</span>
                <span class="letter-size">${formatFileSize(letter.size)}</span>
            </div>
        </div>
    `;
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Update letter counts
function updateLetterCounts() {
    const letterCount = document.getElementById('letterCount');

    if (letterCount) {
        letterCount.textContent = `${appState.displayedLetters.length} letters`;
    }
}

// Update saved count
function updateSavedCount() {
    const savedCount = document.getElementById('savedCount');

    if (savedCount) {
        savedCount.textContent = `${appState.savedItems.length} items`;
    }
}

// Handle search
function handleSearch(e) {
    const query = e.target.value.toLowerCase().trim();
    appState.searchQuery = query;

    const clearBtn = document.getElementById('clearSearch');
    if (clearBtn) {
        clearBtn.style.display = query ? 'block' : 'none';
    }

    filterAndSortLetters();
}

// Clear search
function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    const clearBtn = document.getElementById('clearSearch');

    if (searchInput) searchInput.value = '';
    if (clearBtn) clearBtn.style.display = 'none';

    appState.searchQuery = '';
    filterAndSortLetters();

    // Hide search container
    const searchContainer = document.getElementById('searchContainer');
    if (searchContainer) {
        searchContainer.classList.remove('active');
    }
}

// Handle year filter
function handleYearFilter(e) {
    appState.yearFilter = e.target.value;
    filterAndSortLetters();
}

// Handle sort filter
function handleSortFilter(e) {
    appState.sortBy = e.target.value;
    filterAndSortLetters();
}

// Filter and sort letters
function filterAndSortLetters() {
    console.log('🔍 Filtering and sorting letters...');

    let filtered = [...appState.allLetters];

    // Apply search filter
    if (appState.searchQuery) {
        filtered = filtered.filter(letter =>
            letter.preview.toLowerCase().includes(appState.searchQuery) ||
            letter.category.toLowerCase().includes(appState.searchQuery) ||
            letter.year.includes(appState.searchQuery) ||
            letter.letterId.includes(appState.searchQuery)
        );
    }

    // Apply year filter
    if (appState.yearFilter !== 'all') {
        const [startYear, endYear] = appState.yearFilter.split('-').map(y => parseInt(y));
        filtered = filtered.filter(letter => {
            const year = parseInt(letter.year);
            return year >= startYear && year <= endYear;
        });
    }

    // Apply sorting
    filtered.sort((a, b) => {
        switch (appState.sortBy) {
            case 'newest':
                return parseInt(b.year) - parseInt(a.year) || parseInt(b.letterId) - parseInt(a.letterId);
            case 'oldest':
                return parseInt(a.year) - parseInt(b.year) || parseInt(a.letterId) - parseInt(b.letterId);
            case 'relevance':
                // Simple relevance based on search query match
                if (appState.searchQuery) {
                    const aScore = (a.preview.toLowerCase().match(new RegExp(appState.searchQuery, 'g')) || []).length;
                    const bScore = (b.preview.toLowerCase().match(new RegExp(appState.searchQuery, 'g')) || []).length;
                    return bScore - aScore;
                }
                return parseInt(b.year) - parseInt(a.year);
            default:
                return 0;
        }
    });

    appState.displayedLetters = filtered;
    appState.currentPage = 1;

    if (appState.currentSection === 'letters') {
        renderLetters();
    }
}

// Load more letters
function loadMoreLetters() {
    appState.currentPage++;
    renderLetters();
}

// Open letter modal
function openLetterModal(letterId) {
    console.log(`📖 Opening letter: ${letterId}`);

    const letter = appState.allLetters.find(l => l.id === letterId);
    if (!letter) return;

    const modal = document.getElementById('letterModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');

    if (!modal || !modalTitle || !modalBody) return;

    modalTitle.textContent = `Letter ${letter.year}/${letter.letterId}`;

    // Determine content source and display accordingly
    const isFirebaseContent = letter.source === 'firestore' || letter.source === 'realtimedb';
    const contentSource = isFirebaseContent ? 'Firebase Database' : 'Generated Preview';

    modalBody.innerHTML = `
        <div class="letter-details">
            <div class="detail-row">
                <span class="detail-label">Year:</span>
                <span class="detail-value">${letter.year} (${letter.gregorianYear})</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Letter ID:</span>
                <span class="detail-value">#${letter.letterId}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Category:</span>
                <span class="detail-value">${letter.category}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Content Source:</span>
                <span class="detail-value">${contentSource}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">File Size:</span>
                <span class="detail-value">${formatFileSize(letter.size)}</span>
            </div>
        </div>
        <div class="letter-content">
            <h4>${isFirebaseContent ? 'Full Letter Text:' : 'Letter Preview:'}</h4>
            <div class="letter-text-content">
                ${letter.fullText || letter.preview}
            </div>
            ${isFirebaseContent ? `
                <div class="content-info">
                    <p><strong>✅ This is the actual letter content from the Firebase database</strong></p>
                    <p>Source: ${letter.source === 'firestore' ? 'Cloud Firestore' : 'Realtime Database'}</p>
                </div>
            ` : `
                <div class="content-info">
                    <p><strong>ℹ️ This is a generated preview</strong></p>
                    <p>The full letter content would be available from Firebase database</p>
                </div>
            `}
            <div class="pdf-placeholder">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" fill="currentColor"/>
                    <path d="M14 2V8H20" stroke="white" stroke-width="2"/>
                </svg>
                <p>Click below to open the original PDF letter</p>
                <button class="pdf-btn" onclick="openPDF('${letter.downloadUrl}', {year: '${letter.year}', letterId: '${letter.letterId}'})">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2"/>
                        <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 15V3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Open PDF Letter
                </button>
            </div>
        </div>
    `;

    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// Close letter modal
function closeLetterModal() {
    const modal = document.getElementById('letterModal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
}

// Open PDF
function openPDF(url, letterInfo) {
    try {
        const newWindow = window.open(url, '_blank', 'noopener,noreferrer');

        if (!newWindow) {
            console.warn('Popup blocked, trying alternative method');

            const link = document.createElement('a');
            link.href = url;
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
            link.download = `Letter_${letterInfo.year}_${letterInfo.letterId}.pdf`;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    } catch (error) {
        console.error('Error opening PDF:', error);

        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                alert('PDF URL copied to clipboard. Please paste it in a new browser tab to view the letter.');
            }).catch(() => {
                alert('Unable to open PDF. Please try again or contact support.');
            });
        } else {
            alert('Unable to open PDF. Please try again or contact support.');
        }
    }
}

// Render insights
function renderInsights() {
    console.log('💡 Rendering insights...');

    const insightsList = document.getElementById('insightsList');
    if (!insightsList) return;

    if (appState.displayedInsights.length === 0) {
        insightsList.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                </svg>
                <h3>No insights available</h3>
                <p>Check back later for daily insights from the Rebbe.</p>
            </div>
        `;
        return;
    }

    insightsList.innerHTML = appState.displayedInsights.map(insight => `
        <div class="insight-card">
            <div class="insight-header">
                <span class="insight-date">${insight.date}</span>
            </div>
            <div class="insight-text">${insight.text}</div>
            <div class="insight-actions">
                <button class="save-btn" onclick="toggleSaveInsight('${insight.id}')">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Save
                </button>
            </div>
        </div>
    `).join('');
}

// Render topics
function renderTopics() {
    console.log('🏷️ Rendering topics...');

    const topicsGrid = document.getElementById('topicsGrid');
    if (!topicsGrid) return;

    const topics = [
        { name: 'Education & Torah Study', icon: '📚', count: 1250 },
        { name: 'Family & Marriage', icon: '👨‍👩‍👧‍👦', count: 890 },
        { name: 'Business & Ethics', icon: '💼', count: 650 },
        { name: 'Community Leadership', icon: '🏛️', count: 420 },
        { name: 'Personal Growth', icon: '🌱', count: 780 },
        { name: 'Chassidus & Philosophy', icon: '🔯', count: 950 },
        { name: 'Jewish Law & Practice', icon: '📜', count: 1100 },
        { name: 'Holiday Observance', icon: '🕯️', count: 340 },
        { name: 'Youth & Education', icon: '👶', count: 520 },
        { name: 'Women\'s Role', icon: '👩', count: 380 },
        { name: 'Charity & Kindness', icon: '❤️', count: 290 },
        { name: 'Faith & Spirituality', icon: '✨', count: 670 }
    ];

    topicsGrid.innerHTML = topics.map(topic => `
        <div class="topic-card" onclick="searchByTopic('${topic.name}')">
            <div class="topic-icon">${topic.icon}</div>
            <div class="topic-name">${topic.name}</div>
            <div class="topic-count">${topic.count} letters</div>
        </div>
    `).join('');
}

// Search by topic
function searchByTopic(topicName) {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = topicName;
    }

    appState.searchQuery = topicName.toLowerCase();
    filterAndSortLetters();
    switchTab('letters');

    // Show search container
    const searchContainer = document.getElementById('searchContainer');
    if (searchContainer) {
        searchContainer.classList.add('active');
    }
}

// Render saved items
function renderSaved() {
    console.log('💾 Rendering saved items...');

    const savedList = document.getElementById('savedList');
    if (!savedList) return;

    if (appState.savedItems.length === 0) {
        savedList.innerHTML = `
            <div class="empty-state">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                    <path d="M19 21L12 16L5 21V5C5 4.46957 5.21071 3.96086 5.58579 3.58579C5.96086 3.21071 6.46957 3 7 3H17C17.5304 3 18.0391 3.21071 18.4142 3.58579C18.7893 3.96086 19 4.46957 19 5V21Z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <h3>No saved items yet</h3>
                <p>Tap the bookmark icon on any letter or insight to save it here.</p>
            </div>
        `;
        return;
    }

    const savedLetters = appState.allLetters.filter(letter =>
        appState.savedItems.includes(letter.id)
    );

    savedList.innerHTML = savedLetters.map(letter => createLetterCard(letter)).join('');
}

// Handle keyboard shortcuts
function handleKeyboard(e) {
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'k':
                e.preventDefault();
                toggleSearch();
                break;
            case '1':
                e.preventDefault();
                switchTab('letters');
                break;
            case '2':
                e.preventDefault();
                switchTab('insights');
                break;
            case '3':
                e.preventDefault();
                switchTab('topics');
                break;
            case '4':
                e.preventDefault();
                switchTab('saved');
                break;
        }
    }

    if (e.key === 'Escape') {
        closeLetterModal();

        // Also close search and filter if open
        const searchContainer = document.getElementById('searchContainer');
        const filterPanel = document.getElementById('filterPanel');

        if (searchContainer && searchContainer.classList.contains('active')) {
            searchContainer.classList.remove('active');
        }

        if (filterPanel && filterPanel.classList.contains('active')) {
            filterPanel.classList.remove('active');
        }
    }
}

// Show error message
function showError(message) {
    console.error('❌', message);
    // Could implement a toast notification system here
}

// Toggle save insight
function toggleSaveInsight(insightId) {
    // Implementation for saving insights
    console.log('💾 Toggle save insight:', insightId);
}

// Manual exploration functions for debugging
window.exploreFirebase = async function() {
    console.log('🔍 Manual Firebase exploration triggered...');
    const results = await exploreFirebaseStructure();
    return results;
};

window.testFirebaseConnection = async function() {
    console.log('🔗 Testing Firebase connection...');

    // Test basic connectivity
    try {
        if (db) {
            const testDoc = await db.collection('test').limit(1).get();
            console.log('✅ Firestore connection working');
        }
    } catch (error) {
        console.log('❌ Firestore connection failed:', error.message);
    }

    try {
        if (realtimeDb) {
            const testRef = await realtimeDb.ref('.info/connected').once('value');
            console.log('✅ Realtime DB connection working:', testRef.val());
        }
    } catch (error) {
        console.log('❌ Realtime DB connection failed:', error.message);
    }
};

window.tryDirectFirebaseAccess = async function() {
    console.log('🔧 Trying direct Firebase access methods...');

    // Method 1: Try to list all collections (if permissions allow)
    try {
        const collections = await db.listCollections();
        console.log('📚 Available collections:', collections.map(c => c.id));
    } catch (error) {
        console.log('❌ Cannot list collections:', error.message);
    }

    // Method 2: Try common letter ID patterns
    const letterPatterns = [
        '5752-23455', '23455', 'letter_23455', 'L23455',
        '5743-20586', '20586', 'letter_20586', 'L20586'
    ];

    for (const pattern of letterPatterns) {
        try {
            const doc = await db.collection('letters').doc(pattern).get();
            if (doc.exists) {
                console.log(`✅ Found document with pattern ${pattern}:`, doc.data());
                return doc.data();
            }
        } catch (error) {
            console.log(`❌ Pattern ${pattern} failed:`, error.message);
        }
    }

    // Method 3: Try to access by known letter IDs from your JSON
    console.log('🔍 Trying known letter IDs from JSON data...');
    try {
        const response = await fetch('all_letters_download.json');
        const data = await response.json();

        // Get first few letter IDs
        const sampleLetters = data.all_items.slice(0, 5);

        for (const item of sampleLetters) {
            const pathParts = item.name.split('/');
            const year = pathParts[0];
            const letterId = pathParts[1];

            // Try different document ID formats
            const idFormats = [
                `${year}-${letterId}`,
                letterId,
                `letter_${letterId}`,
                `${year}_${letterId}`,
                item.name.replace(/\//g, '_')
            ];

            for (const docId of idFormats) {
                try {
                    const doc = await db.collection('letters').doc(docId).get();
                    if (doc.exists) {
                        console.log(`✅ FOUND LETTER! ID: ${docId}, Data:`, doc.data());
                        return { id: docId, data: doc.data() };
                    }
                } catch (error) {
                    // Silent fail for this test
                }
            }
        }

        console.log('❌ No letters found with known IDs');
    } catch (error) {
        console.log('❌ Error testing known IDs:', error.message);
    }
};

// Make functions globally available
window.openLetterModal = openLetterModal;
window.openPDF = openPDF;
window.searchByTopic = searchByTopic;
window.toggleSaveInsight = toggleSaveInsight;

// Network analysis for reverse engineering
window.analyzeOriginalApp = function() {
    console.log('📱 Analyzing original app patterns...');

    // Based on your memory about the app URLs
    const knownPatterns = {
        appDomain: 'RebbeResponsa.app',
        urlPattern: 'https://RebbeResponsa.app/letters/{year}/{id}',
        knownYears: ['5711', '5716', '5743'],
        knownIds: ['20586', '29797'],
        firebaseProject: 'rebberesponsa',
        storageBucket: 'english-letters.appspot.com'
    };

    console.log('📊 Known patterns from original app:', knownPatterns);

    // Try to reverse engineer the data structure
    console.log('🔍 Attempting to reverse engineer data structure...');

    // Test if the app uses a specific API endpoint
    const possibleEndpoints = [
        `https://rebberesponsa.firebaseapp.com/api/letters`,
        `https://rebberesponsa.firebaseapp.com/letters`,
        `https://us-central1-rebberesponsa.cloudfunctions.net/getLetters`,
        `https://us-central1-rebberesponsa.cloudfunctions.net/letters`,
        `https://rebberesponsa-default-rtdb.firebaseio.com/letters.json`,
        `https://rebberesponsa-default-rtdb.firebaseio.com/api/letters.json`
    ];

    return Promise.all(possibleEndpoints.map(async (endpoint) => {
        try {
            console.log(`🔍 Testing endpoint: ${endpoint}`);
            const response = await fetch(endpoint);
            const data = await response.text();

            return {
                endpoint,
                status: response.status,
                success: response.ok,
                contentType: response.headers.get('content-type'),
                dataPreview: data.substring(0, 200),
                dataLength: data.length
            };
        } catch (error) {
            return {
                endpoint,
                success: false,
                error: error.message
            };
        }
    }));
};

window.tryMobileAppAPI = async function() {
    console.log('📱 Trying to access mobile app API patterns...');

    // Mobile apps often use specific API patterns
    const mobileAPIPatterns = [
        // Firebase Functions
        `https://us-central1-rebberesponsa.cloudfunctions.net/api/letters`,
        `https://us-central1-rebberesponsa.cloudfunctions.net/getLetters`,
        `https://us-central1-rebberesponsa.cloudfunctions.net/letters/list`,

        // Firebase Hosting API
        `https://rebberesponsa.firebaseapp.com/api/letters.json`,
        `https://rebberesponsa.web.app/api/letters.json`,

        // Direct Firestore REST with different auth
        `https://firestore.googleapis.com/v1/projects/rebberesponsa/databases/(default)/documents:runQuery`,

        // Custom API endpoints
        `https://api.rebberesponsa.app/letters`,
        `https://rebberesponsa.app/api/letters`
    ];

    const results = [];

    for (const endpoint of mobileAPIPatterns) {
        try {
            console.log(`📱 Testing mobile API: ${endpoint}`);

            // Try different HTTP methods and headers
            const methods = ['GET', 'POST'];

            for (const method of methods) {
                try {
                    const options = {
                        method,
                        headers: {
                            'Content-Type': 'application/json',
                            'User-Agent': 'RebbeResponsa/1.3.1 (iOS)',
                            'Accept': 'application/json'
                        }
                    };

                    if (method === 'POST') {
                        options.body = JSON.stringify({
                            action: 'getLetters',
                            limit: 10
                        });
                    }

                    const response = await fetch(endpoint, options);
                    const text = await response.text();

                    if (response.ok && text.length > 0) {
                        console.log(`✅ SUCCESS: ${method} ${endpoint}`);
                        console.log(`📄 Response:`, text.substring(0, 500));

                        results.push({
                            endpoint,
                            method,
                            success: true,
                            status: response.status,
                            data: text
                        });
                    }
                } catch (methodError) {
                    // Silent fail for individual methods
                }
            }
        } catch (error) {
            console.log(`❌ ${endpoint} failed:`, error.message);
        }
    }

    return results;
};

console.log('✅ Rebbe Responsa Web App loaded successfully!');
console.log('🔧 Debug functions available:');
console.log('  - exploreFirebase() - Comprehensive Firebase exploration');
console.log('  - testFirebaseConnection() - Test basic connectivity');
console.log('  - tryDirectFirebaseAccess() - Try direct access methods');
console.log('  - analyzeOriginalApp() - Analyze original app patterns');
console.log('  - tryMobileAppAPI() - Try mobile app API endpoints');
console.log('  - window.firebaseExplorationResults - View last exploration results');
