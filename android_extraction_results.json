{"all_strings": ["00res/color/common_google_signin_btn_text_dark.xml", "11res/color/common_google_signin_btn_text_light.xml", "res/color/common_google_signin_btn_tint.xml", "res/drawable-v21/background.png", "res/drawable-v21/launch_background.xml", "33res/drawable-v21/notification_action_background.xml", "33res/drawable/common_google_signin_btn_icon_dark.xml", "res/drawable/common_google_signin_btn_icon_dark_focused.xml", "res/drawable/common_google_signin_btn_icon_dark_normal.xml", "77res/drawable/common_google_signin_btn_icon_disabled.xml", "44res/drawable/common_google_signin_btn_icon_light.xml", "res/drawable/common_google_signin_btn_icon_light_focused.xml", "res/drawable/common_google_signin_btn_icon_light_normal.xml", "33res/drawable/common_google_signin_btn_text_dark.xml", "res/drawable/common_google_signin_btn_text_dark_focused.xml", "res/drawable/common_google_signin_btn_text_dark_normal.xml", "77res/drawable/common_google_signin_btn_text_disabled.xml", "44res/drawable/common_google_signin_btn_text_light.xml", "res/drawable/common_google_signin_btn_text_light_focused.xml", "res/drawable/common_google_signin_btn_text_light_normal.xml", "res/drawable/download_icon.xml", "res/drawable/notification_bg.xml", "res/drawable/notification_bg_low.xml", "--res/drawable/notification_icon_background.xml", "res/drawable/notification_tile_bg.xml", "res/layout-v21/notification_action.xml", "00res/layout-v21/notification_action_tombstone.xml", "33res/layout-v21/notification_template_custom_big.xml", "33res/layout-v21/notification_template_icon_group.xml", "res/layout/custom_dialog.xml", "res/layout/notification_media_action.xml", "//res/layout/notification_media_cancel_action.xml", "..res/layout/notification_template_big_media.xml", "55res/layout/notification_template_big_media_custom.xml", "55res/layout/notification_template_big_media_narrow.xml", "res/layout/notification_template_big_media_narrow_custom.xml", "00res/layout/notification_template_lines_media.xml", "res/layout/notification_template_media.xml", "11res/layout/notification_template_media_custom.xml", "55res/layout/notification_template_part_chronometer.xml", "..res/layout/notification_template_part_time.xml", "res/menu/example_menu.xml", "res/menu/example_menu2.xml", "res/raw/firebase_common_keep.xml", "res/xml/filepaths.xml", "res/xml/flutter_share_file_paths.xml", "res/xml/splits0.xml", "OOres/drawable-mdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png", "PPres/drawable-mdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png", "OOres/drawable-mdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png", "PPres/drawable-mdpi-v4/common_google_signin_btn_text_light_normal_background.9.png", "22res/drawable-mdpi-v4/googleg_disabled_color_18.png", "22res/drawable-mdpi-v4/googleg_standard_color_18.png", "55res/drawable-mdpi-v4/notification_bg_low_normal.9.png", "66res/drawable-mdpi-v4/notification_bg_low_pressed.9.png", "11res/drawable-mdpi-v4/notification_bg_normal.9.png", "99res/drawable-mdpi-v4/notification_bg_normal_pressed.9.png", "res/drawable-mdpi-v4/notify_panel_notification_icon_bg.png", "res/drawable-mdpi-v4/splash.png", "res/mipmap-mdpi-v4/ic_launcher.png", "--res/mipmap-mdpi-v4/ic_launcher_background.png", "--res/mipmap-mdpi-v4/ic_launcher_foreground.png", "22res/drawable-hdpi-v4/common_full_open_on_phone.png", "OOres/drawable-hdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png", "PPres/drawable-hdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png", "OOres/drawable-hdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png", "PPres/drawable-hdpi-v4/common_google_signin_btn_text_light_normal_background.9.png", "22res/drawable-hdpi-v4/googleg_disabled_color_18.png", "22res/drawable-hdpi-v4/googleg_standard_color_18.png", "55res/drawable-hdpi-v4/notification_bg_low_normal.9.png", "66res/drawable-hdpi-v4/notification_bg_low_pressed.9.png", "11res/drawable-hdpi-v4/notification_bg_normal.9.png", "99res/drawable-hdpi-v4/notification_bg_normal_pressed.9.png", "res/drawable-hdpi-v4/notify_panel_notification_icon_bg.png", "res/drawable-hdpi-v4/splash.png", "res/mipmap-hdpi-v4/ic_launcher.png", "--res/mipmap-hdpi-v4/ic_launcher_background.png", "--res/mipmap-hdpi-v4/ic_launcher_foreground.png", "33res/drawable-xhdpi-v4/common_full_open_on_phone.png", "PPres/drawable-xhdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png", "QQres/drawable-xhdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png", "PPres/drawable-xhdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png", "QQres/drawable-xhdpi-v4/common_google_signin_btn_text_light_normal_background.9.png", "33res/drawable-xhdpi-v4/googleg_disabled_color_18.png", "33res/drawable-xhdpi-v4/googleg_standard_color_18.png", "66res/drawable-xhdpi-v4/notification_bg_low_normal.9.png", "77res/drawable-xhdpi-v4/notification_bg_low_pressed.9.png", "22res/drawable-xhdpi-v4/notification_bg_normal.9.png", "res/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png", "res/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png", "res/drawable-xhdpi-v4/splash.png", "res/mipmap-xhdpi-v4/ic_launcher.png", "..res/mipmap-xhdpi-v4/ic_launcher_background.png", "..res/mipmap-xhdpi-v4/ic_launcher_foreground.png", "QQres/drawable-xxhdpi-v4/common_google_signin_btn_icon_dark_normal_background.9.png", "RRres/drawable-xxhdpi-v4/common_google_signin_btn_icon_light_normal_background.9.png", "QQres/drawable-xxhdpi-v4/common_google_signin_btn_text_dark_normal_background.9.png", "RRres/drawable-xxhdpi-v4/common_google_signin_btn_text_light_normal_background.9.png", "44res/drawable-xxhdpi-v4/googleg_disabled_color_18.png", "44res/drawable-xxhdpi-v4/googleg_standard_color_18.png"], "firebase_strings": {"google_api_key": ["AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"], "storage_bucket": ["english-letters.appspot.com"], "app_id": ["1:300689706865:android:43f7769c0fa6b3c4a7f0d0"], "sender_id": ["300689706865", "300689706865", "300689706865"]}, "potential_secrets": ["AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "english-letters.appspot.com", "1:300689706865:android:43f7769c0fa6b3c4a7f0d0", "300689706865", "300689706865", "300689706865"], "all_secrets": {"resources.arsc": {"api_key": ["AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"]}, "classes.dex": {"firebase_token": ["FIREBASE_INSTALLATIONS_API_DOMAIN", "FIREBASE_INSTALLATIONS_API_VERSION", "FIREBASE_INSTALLATIONS_ID_HEARTBEAT_TAG", "FIREBASE_INSTALLATION_AUTH_VERSION", "FIREBASE_INSTALLATION_ID_KEY", "Firebase-FirebaseInstanceIdServiceConnection", "Firebase-Messaging-Intent-Handle", "Firebase-Messaging-Network-Io", "Firebase-Messaging-Topics-Io", "FirebaseAppLifecycleListener", "FirebaseContextProvider_Factory", "FirebaseInstallationServiceClient", "FirebaseInstallationsException", "FirebaseInstallationsRegistrar", "FirebaseTooManyRequestsException", "FirebaseMessagingBackgroundExecutor", "FirebaseMessagingBackgroundService", "FirebaseMessagingBackgroundService", "FirebaseMessagingInitProvider", "FirebaseFunctionsFactory_Impl", "FirebaseApiNotAvailableException", "FirebaseAppLifecycleListener", "FirebaseTooManyRequestsException", "FirebaseContextProvider_Factory", "FirebaseFunctionsFactory_Impl", "FirebaseInstallationsException", "FirebaseInstallationsException", "FirebaseInstallationsRegistrar", "FirebaseInstallationsRegistrar", "FirebaseInstallationServiceClient", "FirebaseNoSignedInUserException", "FirebaseMessagingBackgroundExecutor", "FirebaseMessagingBackgroundExecutor", "FirebaseMessagingBackgroundExecutor", "FirebaseMessagingBackgroundExecutor", "FirebaseMessagingBackgroundExecutor", "FirebaseMessagingBackgroundService", "FirebaseMessagingBackgroundService", "FirebaseMessagingInitProvider", "FirebaseMessagingBackgroundService", "FirebaseInstallationsException", "FirebaseInstallationRequestBody", "firebase-measurement-connector", "firebase-installations-executor-", "firebaseFunctionsFactoryProvider", "firebaseMessagingServiceClassName", "firebase_analytics_collection_deactivated", "firebase_analytics_collection_enabled", "firebase_data_collection_default_enabled", "firebase_messaging_auto_init_enabled", "firebase_messaging_notification_delegation_enabled", "FirebaseMessagingForArguments", "FirebaseMessagingBackgroundExecutor", "FirebaseMessagingBackgroundExecutor", "firebase_messaging_background"], "auth_token": ["AUTH_TOKEN_EXPIRATION_BUFFER_IN_SECS", "AUTH_TOKEN_REQUEST_RESOURCE_NAME_FORMAT", "AuthTokenRequestBodyToOutputStream"], "project_id": ["rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>"]}, ".\\all_letters_download.json": {"api_key": ["AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"]}, ".\\firebase-auth-interop.properties": {"firebase_token": ["firebase-auth-interop_client"]}, ".\\firebase-datatransport.properties": {"firebase_token": ["firebase-datatransport_client"]}, ".\\firebase-encoders-json.properties": {"firebase_token": ["firebase-encoders-json_client"]}, ".\\firebase-encoders-proto.properties": {"firebase_token": ["firebase-encoders-proto_client"]}, ".\\firebase-installations-interop.properties": {"firebase_token": ["firebase-installations-interop", "firebase-installations-interop_client"]}, ".\\firebase-measurement-connector.properties": {"firebase_token": ["firebase-measurement-connector", "firebase-measurement-connector_client"]}, ".\\firebase_auth_attempts.json": {"project_id": ["rebbe-responsa", "rebberes<PERSON><PERSON><PERSON>"]}, ".\\firebase_exploration_results.json": {"project_id": ["rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>"]}, ".\\firebase_extraction_results.json": {"api_key": ["AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"], "project_id": ["rebberes<PERSON><PERSON><PERSON>", "rebbe-responsa", "rebberes<PERSON><PERSON><PERSON>"]}, ".\\mobile_app_analysis.json": {"api_key": ["AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"], "firebase_token": ["firebase_exploration_results", "firebase_exploration_results", "firebase_exploration_results", "firebase_exploration_results", "firebase_exploration_results"], "project_id": ["rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>", "rebberes<PERSON><PERSON><PERSON>"]}, ".\\rebbe_letters_analysis.json": {"project_id": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, ".\\working_firebase_access.json": {"api_key": ["AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM", "AIzaSyCVR42ZQtxrIU0IPhbbuYtWUCnF7eTjxWM"], "project_id": ["rebberes<PERSON><PERSON><PERSON>"]}}, "auth_success": false}